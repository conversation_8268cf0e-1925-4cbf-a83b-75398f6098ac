# Simple 1-line interface for Multimodel LLM queries

def multimodel_query(prompt="List 3 AI agriculture companies", models=None, parallel=True, return_extended=False):
    """One-liner to query multiple LLM models and get aggregated responses"""
    try:
        import sys
        import os
        
        # Add the project root to path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from gaia.gaia_elf.multimodel.multimodel_elf import PromptMultimodel<PERSON>son, ParallelPromptMultimodelJson
        
        # Default models if none provided
        if models is None:
            models = [
                #"anthropic/claude-sonnet-4-20250514",
                "gpt-4o",
                "gemini/gemini-2.5-pro"
            ]
        
        # Choose processing strategy
        if parallel:
            generator = ParallelPromptMultimodelJson(models=models)
        else:
            generator = PromptMultimodelJson(models=models)
        
        # Generate responses
        if return_extended:
            results, operational, extended_info = generator.generate(prompt, return_extended=True)
            return {
                'results': results,
                'operational': operational,
                'extended_info': extended_info,
                'models': models,
                'parallel': parallel,
                'prompt': prompt
            }
        else:
            results, operational = generator.generate(prompt, return_extended=False)
            return {
                'results': results,
                'operational': operational,
                'models': models,
                'parallel': parallel,
                'prompt': prompt
            }
        
    except Exception as e:
        return {
            'error': f"Error in multimodel_query: {str(e)}",
            'results': {},
            'operational': {},
            'models': models or [],
            'parallel': parallel,
            'prompt': prompt
        }

def multimodel_compare(prompt="Compare pros and cons of electric vs gas vehicles", models=None):
    """Compare responses from multiple models on the same prompt"""
    result = multimodel_query(prompt=prompt, models=models, parallel=True, return_extended=False)
    
    if 'error' in result:
        return result
    
    # Format for easy comparison
    comparison = {
        'prompt': prompt,
        'models_compared': len(result['models']),
        'successful_responses': len([r for r in result['results'].values() if r is not None]),
        'model_responses': {},
        'performance_summary': {}
    }
    
    for model, response in result['results'].items():
        comparison['model_responses'][model] = {
            'response': response,
            'success': response is not None,
            'latency': result['operational'][model].get('latency', 0),
            'error': result['operational'][model].get('error_message')
        }
    
    # Performance summary
    latencies = [op.get('latency', 0) for op in result['operational'].values() if op.get('error_code') == 0]
    if latencies:
        comparison['performance_summary'] = {
            'avg_latency': sum(latencies) / len(latencies),
            'min_latency': min(latencies),
            'max_latency': max(latencies),
            'success_rate': len(latencies) / len(result['models'])
        }
    
    return comparison

def multimodel_consensus(prompt="What is the capital of France?", models=None, consensus_threshold=0.5):
    """Find consensus among multiple models"""
    result = multimodel_query(prompt=prompt, models=models, parallel=True)
    
    if 'error' in result:
        return result
    
    # Analyze responses for consensus
    responses = [r for r in result['results'].values() if r is not None]
    
    consensus_analysis = {
        'prompt': prompt,
        'total_models': len(result['models']),
        'successful_responses': len(responses),
        'consensus_threshold': consensus_threshold,
        'consensus_found': False,
        'consensus_response': None,
        'all_responses': result['results'],
        'performance': result['operational']
    }
    
    if len(responses) >= 2:
        # Simple consensus: check if responses are similar (this is basic - could be enhanced)
        response_texts = [str(r) for r in responses]
        unique_responses = list(set(response_texts))
        
        if len(unique_responses) == 1:
            consensus_analysis['consensus_found'] = True
            consensus_analysis['consensus_response'] = responses[0]
        elif len(responses) * consensus_threshold <= len(responses) - len(unique_responses) + 1:
            # If most responses are similar
            consensus_analysis['consensus_found'] = True
            consensus_analysis['consensus_response'] = responses[0]  # Take first as representative
    
    return consensus_analysis

def multimodel_list_generation(prompt="List 5 AgTech startups", models=None):
    """Generate lists using multiple models and deduplicate"""
    try:
        import sys
        import os
        
        # Add the project root to path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        from gaia.gaia_elf.multimodel.multimodel_list_elf import MultimodelJsonListGeneratorElf
        
        # Default models if none provided
        if models is None:
            models = [
                "anthropic/claude-sonnet-4-20250514",
                "gpt-4o",
                "gpt-4o-mini"
            ]
        
        # Create list generator
        list_generator = MultimodelJsonListGeneratorElf.create_parallel(models=models)
        
        # Generate lists
        results = list_generator.generate_lists(prompt)
        
        return {
            'prompt': prompt,
            'models': models,
            'results': results,
            'type': 'list_generation'
        }
        
    except Exception as e:
        return {
            'error': f"Error in multimodel_list_generation: {str(e)}",
            'prompt': prompt,
            'models': models or [],
            'type': 'list_generation'
        }

def multimodel_fast(prompt="What is AI?", num_models=3):
    """Quick multimodel query with default fast models"""
    fast_models = [
        "gpt-4o-mini",
        "anthropic/claude-3-5-sonnet-20241022", 
        "gemini/gemini-2.0-flash-001"
    ][:num_models]
    
    return multimodel_query(prompt=prompt, models=fast_models, parallel=True)

def multimodel_premium(prompt="Analyze the future of AI", num_models=3):
    """Premium multimodel query with best models"""
    premium_models = [
        "anthropic/claude-sonnet-4-20250514",
        "gpt-4o",
        "o1-preview"
    ][:num_models]
    
    return multimodel_query(prompt=prompt, models=premium_models, parallel=True, return_extended=True)

def multimodel_thinking(prompt="Solve this complex problem step by step"):
    """Use thinking models for complex reasoning"""
    thinking_models = [
        "o1-preview",
        "gemini/gemini-2.0-flash-thinking-exp",
        "anthropic/claude-sonnet-4-20250514"
    ]
    
    return multimodel_query(prompt=prompt, models=thinking_models, parallel=True, return_extended=True)

def get_available_models():
    """Get list of available models for multimodel queries"""
    return {
        'fast_models': [
            "gpt-4o-mini",
            "anthropic/claude-3-5-sonnet-20241022",
            "gemini/gemini-2.0-flash-001"
        ],
        'premium_models': [
            "anthropic/claude-sonnet-4-20250514",
            "gpt-4o",
            "o1-preview"
        ],
        'thinking_models': [
            "o1-preview",
            "gemini/gemini-2.0-flash-thinking-exp"
        ],
        'all_models': [
            "gemini/gemini-2.0-flash-001",
            "gemini/gemini-2.0-flash-thinking-exp",
            "o1-preview",
            "gpt-4o-mini",
            "gpt-4o",
            "anthropic/claude-sonnet-4-20250514",
            "anthropic/claude-3-5-sonnet-20241022",
            "perplexity/sonar"
        ]
    }

# Usage examples:
# result = multimodel_query("What is the future of AI?")
# comparison = multimodel_compare("Compare Python vs JavaScript")
# consensus = multimodel_consensus("What is 2+2?")
# lists = multimodel_list_generation("List top 5 programming languages")
# fast_result = multimodel_fast("Quick question about AI")
# premium_result = multimodel_premium("Complex analysis needed")
# thinking_result = multimodel_thinking("Solve this step by step")
# models = get_available_models()

if __name__ == "__main__":
    """Test the multimodel sprite when run directly"""
    print("Testing spr_multimodel sprite...")
    
    # Test basic multimodel query
    try:
        result = multimodel_fast("What is artificial intelligence?", num_models=2)
        if 'error' not in result:
            print(f"✓ Basic multimodel test passed - got {len(result['results'])} model responses")
            successful = len([r for r in result['results'].values() if r is not None])
            print(f"  Successful responses: {successful}/{len(result['models'])}")
        else:
            print(f"✗ Basic multimodel test failed: {result['error']}")
    except Exception as e:
        print(f"✗ Basic multimodel test failed: {e}")
    
    # Test model comparison
    try:
        comparison = multimodel_compare("What is 2+2?", models=["gpt-4o-mini", "anthropic/claude-3-5-sonnet-20241022"])
        if 'error' not in comparison:
            print(f"✓ Model comparison test passed - compared {comparison['models_compared']} models")
            print(f"  Success rate: {comparison.get('performance_summary', {}).get('success_rate', 0):.2f}")
        else:
            print(f"✗ Model comparison test failed: {comparison['error']}")
    except Exception as e:
        print(f"✗ Model comparison test failed: {e}")
    
    # Test available models
    try:
        models = get_available_models()
        print(f"✓ Available models test passed - found {len(models['all_models'])} total models")
        print(f"  Fast models: {len(models['fast_models'])}")
        print(f"  Premium models: {len(models['premium_models'])}")
    except Exception as e:
        print(f"✗ Available models test failed: {e}")
    
    print("spr_multimodel test complete.")
