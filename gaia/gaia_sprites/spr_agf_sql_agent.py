# AgFunder SQL Agent Sprite - Natural Language to SQL queries

def sql_query(query, db="agbase_reportdb", execute=True, format_type="dict", model=None, debug=False):
    """
    One-liner to run natural language SQL queries against AgFunder databases.
    
    Args:
        query (str): Natural language query (e.g., "count rounds from facebook.com")
        db (str): Database to query - options: agbase_reportdb, agbasedb, cb (default: agbase_reportdb)
        execute (bool): Whether to execute the SQL or just generate it (default: True)
        format_type (str): Output format - dict, json, sql, human, csv (default: dict)
        model (str): LLM model to use (default: anthropic/claude-sonnet-4-20250514)
        debug (bool): Enable debug output (default: False)
    
    Returns:
        tuple: (results, metadata) where results is the query results and metadata contains stats
        
    Examples:
        # Basic query
        results, stats = sql_query("count rounds from facebook.com")
        
        # Query different database
        results, stats = sql_query("find AI companies", db="cb")
        
        # Generate SQL only (don't execute)
        results, stats = sql_query("show investors", execute=False)
        
        # Get raw SQL
        results, stats = sql_query("count organizations", format_type="sql")

        # Get CSV format
        results, stats = sql_query("show top companies", format_type="csv")
    """
    try:
        import sys
        import os
        import json
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # Add sql_agent to path
        sql_agent_path = os.path.join(project_root, 'gaia', 'gaia_elf', 'sql_agent')
        if sql_agent_path not in sys.path:
            sys.path.insert(0, sql_agent_path)
        
        from db_agent_factory import create_agent
        
        # Initialize agent for the specified database
        agent = create_agent(database=db, model=model, debug=debug)
        
        # Test connection first
        conn_test = agent.test_connection()
        if not conn_test['success']:
            error_msg = f"Database connection failed: {conn_test['message']}"
            return None, {
                'error': error_msg,
                'success': False,
                'database': db,
                'query': query
            }
        
        # Run the query
        result = agent.query(query, execute=execute)
        result['success'] = True
        result['database'] = db
        
        # Extract results and metadata
        sql_gen = result['sql_generation']
        sql_exec = result.get('sql_execution')
        
        metadata = {
            'success': True,
            'database': db,
            'query': query,
            'sql_query': sql_gen.get('sql_query', ''),
            'execute': execute,
            'format_type': format_type
        }
        
        # Handle different format types
        if format_type == "sql":
            return sql_gen.get('sql_query', ''), metadata

        elif format_type == "json":
            return json.dumps(result, indent=2), metadata

        elif format_type == "csv":
            # Generate CSV format (formal, quoted)
            if execute and sql_exec and sql_exec.get('success'):
                results = sql_exec.get('results', [])
                if results:
                    import csv
                    import io

                    output = io.StringIO()
                    if len(results) > 0:
                        # Get field names from first row
                        fieldnames = list(results[0].keys())
                        writer = csv.DictWriter(output, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
                        writer.writeheader()
                        writer.writerows(results)

                    csv_content = output.getvalue()
                    output.close()
                    metadata['row_count'] = len(results)
                    return csv_content, metadata
                else:
                    return "", metadata
            else:
                return "# CSV format requires execute=True and successful query execution", metadata
        
        elif format_type == "human":
            # Format for human-readable output
            output_lines = []
            output_lines.append(f"🔍 Query: {query}")
            output_lines.append(f"📊 Database: {db}")
            output_lines.append(f"🔧 Generated SQL: {sql_gen.get('sql_query', 'N/A')}")
            
            if execute and sql_exec:
                if sql_exec.get('success'):
                    rows = sql_exec.get('results', [])
                    output_lines.append(f"✅ Results: {len(rows)} rows")
                    if rows:
                        # Show first few rows
                        for i, row in enumerate(rows[:5]):
                            output_lines.append(f"  Row {i+1}: {row}")
                        if len(rows) > 5:
                            output_lines.append(f"  ... and {len(rows) - 5} more rows")
                else:
                    output_lines.append(f"❌ Execution failed: {sql_exec.get('error', 'Unknown error')}")
            elif not execute:
                output_lines.append("⏸️  SQL generated but not executed")
            
            return "\n".join(output_lines), metadata
        
        else:  # format_type == "dict" (default)
            if execute and sql_exec and sql_exec.get('success'):
                results = sql_exec.get('results', [])
                metadata['row_count'] = len(results)
                metadata['execution_time'] = sql_exec.get('execution_time')
                return results, metadata
            else:
                # Return the SQL generation result
                return sql_gen, metadata
        
    except Exception as e:
        error_msg = f"Error in sql_query: {str(e)}"
        if debug:
            import traceback
            error_msg += f"\n{traceback.format_exc()}"
        
        return None, {
            'error': error_msg,
            'success': False,
            'database': db,
            'query': query
        }


def list_databases():
    """
    List available databases and their descriptions.
    
    Returns:
        dict: Dictionary of available databases with their metadata
    """
    try:
        import sys
        import os
        
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # Add sql_agent to path
        sql_agent_path = os.path.join(project_root, 'gaia', 'gaia_elf', 'sql_agent')
        if sql_agent_path not in sys.path:
            sys.path.insert(0, sql_agent_path)
        
        from db_agent_factory import get_available_databases
        
        return get_available_databases()
        
    except Exception as e:
        return {'error': f"Error listing databases: {str(e)}"}


def quick_sql(query, db="agbase_reportdb"):
    """
    Ultra-simple one-liner for quick SQL queries.
    
    Args:
        query (str): Natural language query
        db (str): Database name (default: agbase_reportdb)
    
    Returns:
        list: Query results as list of dictionaries
    """
    results, _ = sql_query(query, db=db, execute=True, format_type="dict")
    return results if results is not None else []


# Usage examples and testing
if __name__ == "__main__":
    """Test the SQL agent sprite when run directly"""
    print("Testing spr_agf_sql_agent sprite...")
    
    try:
        # Test 1: List databases
        print("\n1. Testing list_databases()...")
        dbs = list_databases()
        if 'error' in dbs:
            print(f"❌ Database listing failed: {dbs['error']}")
        else:
            print(f"✅ Found {len(dbs)} databases:")
            for db_id, db_info in dbs.items():
                print(f"   {db_id}: {db_info.get('description', 'No description')}")
        
        # Test 2: Simple query
        print("\n2. Testing simple query...")
        results, stats = sql_query("count organizations", db="agbase_reportdb")
        if stats.get('success'):
            print(f"✅ Query successful - got {len(results) if results else 0} results")
            print(f"   SQL: {stats.get('sql_query', 'N/A')}")
        else:
            print(f"❌ Query failed: {stats.get('error', 'Unknown error')}")
        
        # Test 3: SQL generation only
        print("\n3. Testing SQL generation only...")
        sql_only, stats = sql_query("show all companies", execute=False, format_type="sql")
        if stats.get('success'):
            print(f"✅ SQL generation successful")
            print(f"   Generated SQL: {sql_only}")
        else:
            print(f"❌ SQL generation failed: {stats.get('error', 'Unknown error')}")

        # Test 4: CSV format
        print("\n4. Testing CSV format...")
        csv_output, stats = sql_query("show top 3 companies", format_type="csv")
        if stats.get('success'):
            print(f"✅ CSV generation successful")
            lines = csv_output.split('\n')
            print(f"   CSV lines: {len([l for l in lines if l.strip()])} (including header)")
            if len(lines) > 0:
                print(f"   Header: {lines[0][:100]}...")
        else:
            print(f"❌ CSV generation failed: {stats.get('error', 'Unknown error')}")

        # Test 5: Quick SQL helper
        print("\n5. Testing quick_sql helper...")
        quick_results = quick_sql("count funding rounds")
        print(f"✅ Quick SQL returned {len(quick_results)} results")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nspr_agf_sql_agent test complete.")
