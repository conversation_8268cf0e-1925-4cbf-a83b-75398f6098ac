
from .spr_agf_agsearch import quick_search
from .spr_tamarind import market_research
from .spr_ext_search import web_search, news_search
from .spr_agf_omnisearch import omni_search
from .spr_ext_wiki import wiki_search
from .spr_agf_multimodel import multimodel_query
from .spr_agf_frames import frame_search, frame_list
from .spr_agf_investors import investor_search, investor_stats
from .spr_llm_smart import (
    llm_completion_text, llm_completion_json, llm_analyze_code,
    llm_generate_code, llm_explain_concept, llm_debug_error
)
from .spr_ext_deep_research import (
    deep_research_simple, deep_research_robust, deep_research_batch,
    deep_research_load, deep_research_list
)
from .spr_ext_babyfetch_simple import (
    babyfetch_simple, babyfetch_text_only, babyfetch_multiple_simple
)
from .spr_elf_mem import (
    elf_mem_store, elf_mem_get, elf_mem_delete,
    elf_mem_list_keys, elf_mem_bank_status, elf_mem_list_banks
)
from .spr_simple_chart import (
    simple_chart_svg, simple_chart_png, quick_chart
)
from .spr_agf_agsearch_models import (
    list_contexts, search_contexts, fetch_context, create_context,
    list_ratings_for_context, create_rating, delete_rating
)
from .spr_agf_debugger import (
    debug_database_connection, debug_django_setup,
    debug_sprite_function, debug_mcp_context
)
from .spr_agf_sql_agent import (
    sql_query, list_databases, quick_sql
)
from .spr_ext_babyfetch import (
    babyfetch_url
)

# Create namespace for sprite_l1 tools
class sprite_l1:
    """Namespace for sprite level 1 tools"""
    # Core AgFunder sprites
    quick_search = quick_search
    market_research = market_research
    web_search = web_search
    news_search = news_search
    omni_search = omni_search
    wiki_search = wiki_search
    multimodel_query = multimodel_query
    frame_search = frame_search
    frame_list = frame_list
    investor_search = investor_search
    investor_stats = investor_stats

    # LLM Smart tools
    llm_completion_text = llm_completion_text
    llm_completion_json = llm_completion_json
    llm_analyze_code = llm_analyze_code
    llm_generate_code = llm_generate_code
    llm_explain_concept = llm_explain_concept
    llm_debug_error = llm_debug_error

    # Deep research tools
    deep_research_simple = deep_research_simple
    deep_research_robust = deep_research_robust
    deep_research_batch = deep_research_batch
    deep_research_load = deep_research_load
    deep_research_list = deep_research_list

    # BabyFetch tools
    babyfetch_simple = babyfetch_simple
    babyfetch_text_only = babyfetch_text_only
    babyfetch_multiple_simple = babyfetch_multiple_simple
    babyfetch_url = babyfetch_url

    # ELF Memory tools
    elf_mem_store = elf_mem_store
    elf_mem_get = elf_mem_get
    elf_mem_delete = elf_mem_delete
    elf_mem_list_keys = elf_mem_list_keys
    elf_mem_bank_status = elf_mem_bank_status
    elf_mem_list_banks = elf_mem_list_banks

    # Chart tools
    simple_chart_svg = simple_chart_svg
    simple_chart_png = simple_chart_png
    quick_chart = quick_chart

    # AgSearch Models tools
    list_contexts = list_contexts
    search_contexts = search_contexts
    fetch_context = fetch_context
    create_context = create_context
    list_ratings_for_context = list_ratings_for_context
    create_rating = create_rating
    delete_rating = delete_rating

    # Debug tools
    debug_database_connection = debug_database_connection
    debug_django_setup = debug_django_setup
    debug_sprite_function = debug_sprite_function
    debug_mcp_context = debug_mcp_context

    # SQL Agent tools
    sql_query = sql_query
    list_databases = list_databases
    quick_sql = quick_sql

# Helper function to get all sprite functions for iteration
def get_all_sprite_functions():
    """
    Get all sprite functions as a list of (name, function) tuples.
    Useful for iterating over all available sprite tools.
    """
    sprite_functions = []
    for attr_name in dir(sprite_l1):
        if not attr_name.startswith('_'):  # Skip private attributes
            attr_value = getattr(sprite_l1, attr_name)
            if callable(attr_value):  # Only include callable functions
                sprite_functions.append((attr_name, attr_value))
    return sprite_functions

# Helper function to get sprite functions as a dictionary
def get_sprite_functions_dict():
    """
    Get all sprite functions as a dictionary mapping name -> function.
    Useful for dynamic tool registration.
    """
    return {name: func for name, func in get_all_sprite_functions()}
