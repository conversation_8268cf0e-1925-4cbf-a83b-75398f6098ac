# -*- coding: utf-8 -*-
"""
AGFunder AgSearch Models Sprite

Provides access to legacy agsearch models (AsContext and AsRating) from Django 1.11 database.
Offers consistent CRUD operations for contexts and ratings with improved naming conventions.

Functions:
- AsContext: list_contexts, search_contexts, fetch_context, create_context
- AsRating: list_ratings_for_context, create_rating, delete_rating
"""

import sys
import os
import django
from django.conf import settings
from typing import List, Dict, Optional, Any


def _setup_django():
    """Setup Django environment for legacy database access"""
    try:
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        # Add the djangaia directory to Python path for Django apps like coresignal_browser
        djangaia_dir = os.path.join(project_root, 'gaia', 'djangaia')
        if djangaia_dir not in sys.path:
            sys.path.insert(0, djangaia_dir)

        # Setup Django if not already configured
        if not settings.configured:
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gaia.djangaia.djangaia.settings')
            django.setup()

        return True
    except Exception as e:
        print(f"Django setup error: {e}")
        return False


def _get_legacy_managers():
    """Get legacy model managers with error handling"""
    try:
        from legacy_agsearch.utils import (
            LegacyAsContextManager,
            LegacyAsRatingManager
        )
        return LegacyAsContextManager, LegacyAsRatingManager
    except ImportError as e:
        print(f"Import error: {e}")
        return None, None


def _context_to_dict(context) -> Dict[str, Any]:
    """Convert AsContext model instance to dictionary"""
    if not context:
        return {}
    
    return {
        'id': context.id,
        'name': context.name,
        'context_type': context.context_type,
        'keywords': context.keywords,
        'comments': context.comments,
        'tagtree_id': context.tagtree_id,
        'created_by_id': context.created_by_id if context.created_by else None,
        'created_by_username': context.created_by.username if context.created_by else None,
        'created_at': context.created_at.isoformat() if context.created_at else None,
    }


def _rating_to_dict(rating) -> Dict[str, Any]:
    """Convert AsRating model instance to dictionary"""
    if not rating:
        return {}
    
    return {
        'id': rating.id,
        'es_id': rating.es_id,
        'es_idx': rating.es_idx,
        'rating': rating.rating,
        'context_id': rating.context_id,
        'context_name': rating.context.name if rating.context else None,
        'created_by_id': rating.created_by_id if rating.created_by else None,
        'created_by_username': rating.created_by.username if rating.created_by else None,
        'created_at': rating.created_at.isoformat() if rating.created_at else None,
        'goid': rating.goid,
    }


# AsContext Operations
def list_contexts(limit: int = 100) -> List[Dict[str, Any]]:
    """
    List AsContext records from legacy database

    Args:
        limit: Maximum number of records to return

    Returns:
        List of context dictionaries
    """
    print(f"🔍 DEBUG: list_contexts called with limit={limit}")

    if not _setup_django():
        print("🔍 DEBUG: Django setup failed")
        return []

    # DEBUG: Check Django database connections (async-safe)
    try:
        from django.db import connections
        from asgiref.sync import sync_to_async
        import asyncio

        legacy_conn = connections['legacy']
        print(f"🔍 DEBUG: Legacy connection: {legacy_conn}")
        print(f"🔍 DEBUG: Legacy connection settings: {legacy_conn.settings_dict}")

        # Test raw SQL query using sync_to_async for MCP compatibility
        def _execute_raw_query():
            with legacy_conn.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM agsearch_ascontext")
                raw_count = cursor.fetchone()[0]
                print(f"🔍 DEBUG: Raw SQL count: {raw_count} contexts in database")

                if raw_count > 0:
                    cursor.execute("SELECT id, name, context_type FROM agsearch_ascontext LIMIT 3")
                    raw_rows = cursor.fetchall()
                    print(f"🔍 DEBUG: Raw SQL sample: {raw_rows}")
                return raw_count

        # Check if we're in an async context and handle accordingly
        try:
            # Try to get current event loop - if this succeeds, we're in async context
            loop = asyncio.get_running_loop()
            print(f"🔍 DEBUG: Running in async context, using sync_to_async")
            # We're in async context, use sync_to_async
            raw_count = sync_to_async(_execute_raw_query)()
            # Note: This will be a coroutine, but we can't await it here
            print(f"🔍 DEBUG: Async query initiated")
        except RuntimeError:
            # No event loop running, we're in sync context
            print(f"🔍 DEBUG: Running in sync context, executing directly")
            raw_count = _execute_raw_query()

    except Exception as db_e:
        print(f"🔍 DEBUG: Database connection error: {db_e}")
        # Don't return empty list, continue with Django ORM which should work
        print(f"🔍 DEBUG: Continuing with Django ORM despite raw SQL error")

    ContextManager, _ = _get_legacy_managers()
    if not ContextManager:
        print("🔍 DEBUG: ContextManager not available")
        return []

    try:
        print("🔍 DEBUG: Calling ContextManager.get_all()")

        # Check if we're in async context and handle Django ORM accordingly
        from asgiref.sync import sync_to_async
        import asyncio

        def _get_contexts_sync():
            """Synchronous function to get contexts"""
            contexts = ContextManager.get_all()
            print(f"🔍 DEBUG: ContextManager returned: {type(contexts)}")

            # Apply limit and convert to dictionaries
            contexts_list = list(contexts[:limit])
            print(f"🔍 DEBUG: Final contexts_list length: {len(contexts_list)}")

            result = [_context_to_dict(ctx) for ctx in contexts_list]
            print(f"🔍 DEBUG: Returning {len(result)} contexts")
            return result

        try:
            # Check if we're in an async context
            loop = asyncio.get_running_loop()
            print(f"🔍 DEBUG: In async context, using sync_to_async for Django ORM")
            # We're in async context, but we can't await here since this function is not async
            # Instead, we'll run the sync function in a thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_get_contexts_sync)
                result = future.result(timeout=30)  # 30 second timeout
                return result
        except RuntimeError:
            # No event loop running, we're in sync context
            print(f"🔍 DEBUG: In sync context, executing Django ORM directly")
            return _get_contexts_sync()

    except Exception as e:
        print(f"🔍 DEBUG: Error listing contexts: {e}")
        import traceback
        traceback.print_exc()
        return []


def search_contexts(name_query: str, limit: int = 50) -> List[Dict[str, Any]]:
    """
    Search AsContext records by name
    
    Args:
        name_query: Search term for context name (case-insensitive contains)
        limit: Maximum number of records to return
        
    Returns:
        List of matching context dictionaries
    """
    if not _setup_django():
        return []
    
    ContextManager, _ = _get_legacy_managers()
    if not ContextManager:
        return []
    
    try:
        contexts = ContextManager.get_by_name(name_query)
        contexts_list = list(contexts[:limit])
        return [_context_to_dict(ctx) for ctx in contexts_list]
        
    except Exception as e:
        print(f"Error searching contexts: {e}")
        return []


def fetch_context(context_id: int) -> Optional[Dict[str, Any]]:
    """
    Fetch a specific AsContext by ID
    
    Args:
        context_id: ID of the context to fetch
        
    Returns:
        Context dictionary or None if not found
    """
    if not _setup_django():
        return None
    
    ContextManager, _ = _get_legacy_managers()
    if not ContextManager:
        return None
    
    try:
        context = ContextManager.get_by_id(context_id)
        return _context_to_dict(context) if context else None
        
    except Exception as e:
        print(f"Error fetching context {context_id}: {e}")
        return None


def create_context(name: str, context_type: str = 'draft', keywords: Optional[str] = None, 
                  comments: Optional[str] = None, created_by_id: Optional[int] = None,
                  tagtree_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
    """
    Create a new AsContext record
    
    Args:
        name: Context name (required)
        context_type: Type ('draft', 'tag', 'client', 'compete', 'junk')
        keywords: Keywords text
        comments: Comments text
        created_by_id: User ID who created this context
        tagtree_id: TagTree ID if applicable
        
    Returns:
        Created context dictionary or None if failed
    """
    if not _setup_django():
        return None
    
    ContextManager, _ = _get_legacy_managers()
    if not ContextManager:
        return None
    
    try:
        context = ContextManager.create(
            name=name,
            context_type=context_type,
            keywords=keywords,
            comments=comments,
            created_by_id=created_by_id,
            tagtree_id=tagtree_id
        )
        return _context_to_dict(context)
        
    except Exception as e:
        print(f"Error creating context: {e}")
        return None


# AsRating Operations
def list_ratings_for_context(context_id: int, limit: int = 1000) -> List[Dict[str, Any]]:
    """
    List all AsRating records for a specific context
    
    Args:
        context_id: ID of the context to get ratings for
        limit: Maximum number of records to return
        
    Returns:
        List of rating dictionaries
    """
    if not _setup_django():
        return []
    
    _, RatingManager = _get_legacy_managers()
    if not RatingManager:
        return []
    
    try:
        ratings = RatingManager.get_by_context(context_id)
        ratings_list = list(ratings[:limit])
        return [_rating_to_dict(rating) for rating in ratings_list]
        
    except Exception as e:
        print(f"Error listing ratings for context {context_id}: {e}")
        return []


def create_rating(es_id: str, context_id: int, rating: int = 0, 
                 created_by_id: Optional[int] = None, es_idx: Optional[str] = None,
                 goid: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Create a new AsRating record
    
    Args:
        es_id: Elasticsearch document ID (required)
        context_id: AsContext ID this rating belongs to (required)
        rating: Rating value (typically -1, 0, or 1)
        created_by_id: User ID who created this rating
        es_idx: Elasticsearch index name
        goid: Additional identifier
        
    Returns:
        Created rating dictionary or None if failed
    """
    if not _setup_django():
        return None
    
    _, RatingManager = _get_legacy_managers()
    if not RatingManager:
        return None
    
    try:
        rating_obj = RatingManager.create(
            es_id=es_id,
            context_id=context_id,
            rating=rating,
            created_by_id=created_by_id,
            es_idx=es_idx,
            goid=goid
        )
        return _rating_to_dict(rating_obj)
        
    except Exception as e:
        print(f"Error creating rating: {e}")
        return None


def delete_rating(rating_id: int) -> bool:
    """
    Delete an AsRating record by ID
    
    Args:
        rating_id: ID of the rating to delete
        
    Returns:
        True if deleted successfully, False otherwise
    """
    if not _setup_django():
        return False
    
    try:
        from legacy_agsearch.models import LegacyAsRating
        
        rating = LegacyAsRating.objects.using('legacy').get(id=rating_id)
        rating.delete()
        return True
        
    except Exception as e:
        print(f"Error deleting rating {rating_id}: {e}")
        return False


# Test function
if __name__ == "__main__":
    """Test the agsearch models sprite when run directly"""
    print("Testing spr_agf_agsearch_models sprite...")
    
    try:
        # Test context listing
        contexts = list_contexts(limit=5)
        print(f"✓ Context listing test passed - found {len(contexts)} contexts")
        
        if contexts:
            # Test context fetching
            first_context = fetch_context(contexts[0]['id'])
            print(f"✓ Context fetch test passed - got context: {first_context['name']}")
            
            # Test ratings for context
            ratings = list_ratings_for_context(contexts[0]['id'], limit=5)
            print(f"✓ Ratings listing test passed - found {len(ratings)} ratings for context")
        
        # Test context search
        search_results = search_contexts("test", limit=3)
        print(f"✓ Context search test passed - found {len(search_results)} matching contexts")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
    
    print("spr_agf_agsearch_models test complete.")
