#!/usr/bin/env python3
"""
Sprite module for ELF Memory Bank operations.

Provides high-level memory bank operations for storing, retrieving, and managing
structured data across different memory contexts (global, project, user, etc.).

Memory Bank Hierarchy:
- global/system/core - Core system memory
- global/system/tools - Tool configurations
- market/{market_slug} - Market-specific data
- org/{organization_id} - Organization memory
- project/{project_id} - Project-specific memory
- user/{user_id} - User-specific memory
- convo/{conversation_id} - Conversation memory
"""

import sys
import os

# Add the elf_mem module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'gaia_elf', 'elf_mem'))

try:
    from elf_mem import (
        mem_store, mem_get, mem_delete, mem_list, 
        mem_bank_info, mem_bank_list
    )
except ImportError as e:
    print(f"Warning: Could not import elf_mem module: {e}")
    # Provide stub functions for testing
    def mem_store(bank, key, value): return {"error": "elf_mem not available"}
    def mem_get(bank, key): return None
    def mem_delete(bank, key): return False
    def mem_list(bank, key_prefix=None): return []
    def mem_bank_info(bank): return {"error": "elf_mem not available"}
    def mem_bank_list(): return []


def elf_mem_store(bank: str, key: str, value: dict) -> dict:
    """
    Store a value in a memory bank.
    
    Args:
        bank: Memory bank name (e.g., 'global/system/core', 'project/my_project')
        key: Key to store the value under
        value: Dictionary value to store
        
    Returns:
        dict: Success status and metadata
    """
    try:
        mem_store(bank, key, value)
        return {
            "success": True,
            "bank": bank,
            "key": key,
            "action": "stored",
            "message": f"Successfully stored '{key}' in bank '{bank}'"
        }
    except Exception as e:
        return {
            "success": False,
            "bank": bank,
            "key": key,
            "action": "store_failed",
            "error": str(e)
        }


def elf_mem_get(bank: str, key: str) -> dict:
    """
    Retrieve a value from a memory bank.
    
    Args:
        bank: Memory bank name
        key: Key to retrieve
        
    Returns:
        dict: Retrieved value and metadata
    """
    try:
        value = mem_get(bank, key)
        if value is None:
            return {
                "success": False,
                "bank": bank,
                "key": key,
                "action": "get_failed",
                "error": f"Key '{key}' not found in bank '{bank}'"
            }
        
        return {
            "success": True,
            "bank": bank,
            "key": key,
            "action": "retrieved",
            "value": value,
            "message": f"Successfully retrieved '{key}' from bank '{bank}'"
        }
    except Exception as e:
        return {
            "success": False,
            "bank": bank,
            "key": key,
            "action": "get_failed",
            "error": str(e)
        }


def elf_mem_delete(bank: str, key: str) -> dict:
    """
    Delete a key from a memory bank.
    
    Args:
        bank: Memory bank name
        key: Key to delete
        
    Returns:
        dict: Deletion status and metadata
    """
    try:
        deleted = mem_delete(bank, key)
        return {
            "success": deleted,
            "bank": bank,
            "key": key,
            "action": "deleted" if deleted else "delete_failed",
            "message": f"Successfully deleted '{key}' from bank '{bank}'" if deleted 
                      else f"Key '{key}' not found in bank '{bank}'"
        }
    except Exception as e:
        return {
            "success": False,
            "bank": bank,
            "key": key,
            "action": "delete_failed",
            "error": str(e)
        }


def elf_mem_list_keys(bank: str, key_prefix: str = None) -> dict:
    """
    List keys in a memory bank.
    
    Args:
        bank: Memory bank name
        key_prefix: Optional prefix to filter keys
        
    Returns:
        dict: List of keys and metadata
    """
    try:
        keys = mem_list(bank, key_prefix)
        return {
            "success": True,
            "bank": bank,
            "key_prefix": key_prefix,
            "action": "listed",
            "keys": keys,
            "count": len(keys),
            "message": f"Found {len(keys)} keys in bank '{bank}'"
        }
    except Exception as e:
        return {
            "success": False,
            "bank": bank,
            "key_prefix": key_prefix,
            "action": "list_failed",
            "error": str(e)
        }


def elf_mem_bank_status(bank: str) -> dict:
    """
    Get comprehensive status information about a memory bank.
    
    Args:
        bank: Memory bank name
        
    Returns:
        dict: Bank status information
    """
    try:
        info = mem_bank_info(bank)
        return {
            "success": True,
            "bank": bank,
            "action": "status_retrieved",
            "info": info,
            "message": f"Retrieved status for bank '{bank}'"
        }
    except Exception as e:
        return {
            "success": False,
            "bank": bank,
            "action": "status_failed",
            "error": str(e)
        }


def elf_mem_list_banks() -> dict:
    """
    List all available memory banks.
    
    Returns:
        dict: List of all memory banks
    """
    try:
        banks = mem_bank_list()
        return {
            "success": True,
            "action": "banks_listed",
            "banks": banks,
            "count": len(banks),
            "message": f"Found {len(banks)} memory banks"
        }
    except Exception as e:
        return {
            "success": False,
            "action": "banks_list_failed",
            "error": str(e)
        }





# Test function
def test_elf_mem_sprite():
    """Test the elf_mem sprite functions"""
    print("=== Testing ELF Memory Sprite ===")

    # Test bank listing
    result = elf_mem_list_banks()
    print(f"Banks: {result}")

    # Test explicit store/get with clear bank names
    test_data = {"name": "Test Project", "status": "active", "created": "2025-01-23"}
    bank_name = "project/test_sprite_project"

    store_result = elf_mem_store(bank_name, "config", test_data)
    print(f"Store result: {store_result}")

    get_result = elf_mem_get(bank_name, "config")
    print(f"Get result: {get_result}")

    # Test key listing
    keys_result = elf_mem_list_keys(bank_name)
    print(f"Keys result: {keys_result}")

    # Test bank status
    status_result = elf_mem_bank_status(bank_name)
    print(f"Bank status: {status_result}")

    # Test delete
    delete_result = elf_mem_delete(bank_name, "config")
    print(f"Delete result: {delete_result}")


if __name__ == "__main__":
    test_elf_mem_sprite()
