#!/usr/bin/env python3
"""
Sprite: Simple BabyFetch (Web Scraping/Fetching)

Provides efficient web scraping using BabySpider with manageable output sizes.
This sprite prevents hanging by limiting content length and focusing on key data.
"""

import sys
import os
from typing import Dict, Any, Optional, List
import traceback

def babyfetch_simple(url: str, max_text_length: int = 1500) -> Dict[str, Any]:
    """
    Fetch and parse content from a URL using BabySpider with size limits.
    
    Args:
        url: The URL to fetch content from
        max_text_length: Maximum length of text content to return (default: 1500 chars)
    
    Returns:
        Dict containing:
        - success: bool - Whether the fetch was successful
        - url: str - The original URL
        - content: dict - Parsed content (text, title, links summary)
        - crawl_info: dict - Crawl metadata (status_code, domain, etc.)
        - error: str - Error message if failed
    """
    try:
        # Add parent directories to path for gaia imports
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)  # gaia/gaia_sprites -> gaia
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        
        # Import CachedBabyFetch (more reliable)
        from gaia.babyspider.cached_babyfetch import CachedBabyFetch
        from gaia.babyspider import babyparse

        # Create CachedBabyFetch instance
        cbf = CachedBabyFetch()

        # Fetch URL
        result = cbf.fetch_url_or_get_cached(url=url, max_age=3600)
        
        # Extract results from CachedBabyFetch format
        cache_hit = result.get('cache_hit', False)
        results = result.get('results', {})

        if not results:
            return {
                'success': False,
                'url': url,
                'error': 'No results returned from babyfetch'
            }

        # Get the protocol-specific result (usually 'https' or 'http')
        protocol = list(results.keys())[0]
        data = results[protocol]

        # Extract crawl information
        crawl_info = data.get('crawl', {})
        content_data = data.get('content', {})

        if crawl_info.get('status_code') != 200:
            return {
                'success': False,
                'url': url,
                'error': f"Failed to fetch URL. Status: {crawl_info.get('status_code', 'unknown')}",
                'crawl_info': {
                    'status_code': crawl_info.get('status_code'),
                    'cache_hit': cache_hit
                }
            }

        # Parse HTML content if available
        html = content_data.get('html', '')
        content = {}

        if html:
            try:
                # Extract text content
                text_content = babyparse.html_to_plain(html).strip()

                # Truncate if too long
                if len(text_content) > max_text_length:
                    content['text'] = text_content[:max_text_length] + '...'
                    content['text_truncated'] = True
                    content['full_text_length'] = len(text_content)
                else:
                    content['text'] = text_content
                    content['text_truncated'] = False

                # Extract basic metadata
                content['title'] = babyparse.extract_title(html) if hasattr(babyparse, 'extract_title') else ''
                content['word_count'] = len(text_content.split()) if text_content else 0
                content['char_count'] = len(text_content) if text_content else 0

            except Exception as parse_error:
                content['text'] = html[:max_text_length] + '...' if len(html) > max_text_length else html
                content['parse_error'] = str(parse_error)
                content['word_count'] = 0
                content['char_count'] = len(content['text'])

        return {
            'success': True,
            'url': url,
            'content': content,
            'crawl_info': {
                'status_code': crawl_info.get('status_code'),
                'cache_hit': cache_hit,
                'domain': data.get('metadata', {}).get('domain', ''),
                'final_url': crawl_info.get('final_url', url)
            },
            'error': None
        }
        
    except Exception as e:
        return {
            'success': False,
            'url': url,
            'error': f"BabySpider error: {str(e)}",
            'traceback': traceback.format_exc()
        }

def babyfetch_text_only(url: str, max_length: int = 1000) -> Dict[str, Any]:
    """
    Fetch only the text content from a URL (most efficient).
    
    Args:
        url: The URL to fetch content from
        max_length: Maximum length of text to return
    
    Returns:
        Dict with success, url, text, title, and basic info
    """
    try:
        result = babyfetch_simple(url, max_text_length=max_length)
        
        if not result['success']:
            return result
        
        # Return only essential text data
        return {
            'success': True,
            'url': url,
            'title': result['content'].get('title', ''),
            'text': result['content'].get('text', ''),
            'word_count': result['content'].get('word_count', 0),
            'domain': result['crawl_info'].get('domain', ''),
            'status_code': result['crawl_info'].get('status_code')
        }
        
    except Exception as e:
        return {
            'success': False,
            'url': url,
            'error': f"Text fetch error: {str(e)}"
        }

def babyfetch_multiple_simple(urls: List[str], max_text_length: int = 1000) -> Dict[str, Any]:
    """
    Fetch content from multiple URLs efficiently.
    
    Args:
        urls: List of URLs to fetch
        max_text_length: Maximum text length per URL
    
    Returns:
        Dict with results list and summary
    """
    try:
        results = []
        success_count = 0
        
        for url in urls:
            result = babyfetch_simple(url, max_text_length=max_text_length)
            results.append(result)
            
            if result['success']:
                success_count += 1
        
        return {
            'success': True,
            'results': results,
            'summary': {
                'total_urls': len(urls),
                'successful_fetches': success_count,
                'failed_fetches': len(urls) - success_count,
                'success_rate': success_count / len(urls) if urls else 0
            }
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f"Multiple URL fetch error: {str(e)}"
        }

# Test function
def test_babyfetch_simple():
    """Test the simple babyfetch functionality"""
    print("🧪 Testing Simple BabyFetch Sprite")
    print("=" * 40)
    
    # Test single URL fetch
    test_url = "https://www.agfunder.com/"
    print(f"Testing URL: {test_url}")
    
    result = babyfetch_simple(test_url)
    print(f"Success: {result['success']}")
    
    if result['success']:
        content = result['content']
        print(f"Content length: {content.get('char_count', 0)} chars")
        print(f"Word count: {content.get('word_count', 0)} words")
        print(f"Title: {content.get('title', 'N/A')}")
        print(f"Status code: {result['crawl_info'].get('status_code', 'N/A')}")
        print(f"Text preview: {content.get('text', '')[:100]}...")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    test_babyfetch_simple()
