# -*- coding: utf-8 -*-
"""
AGF Debugger Sprite

Provides debugging utilities for AGF systems, including database connection testing,
Django setup verification, and sprite function diagnostics.

Functions:
- debug_database_connection: Test database connections and query capabilities
- debug_django_setup: Verify Django configuration and app loading
- debug_sprite_function: Test specific sprite function execution
- debug_mcp_context: Debug MCP execution context and environment
"""

import sys
import os
import django
from django.conf import settings
from typing import Dict, Any, List, Optional


def _setup_django():
    """Setup Django environment for debugging"""
    try:
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # Add the djangaia directory to Python path for Django apps
        djangaia_dir = os.path.join(project_root, 'gaia', 'djangaia')
        if djangaia_dir not in sys.path:
            sys.path.insert(0, djangaia_dir)
        
        # Setup Django if not already configured
        if not settings.configured:
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gaia.djangaia.djangaia.settings')
            django.setup()
        
        return True
    except Exception as e:
        print(f"Django setup error: {e}")
        return False


def debug_database_connection(database_name: str = 'legacy') -> Dict[str, Any]:
    """
    Test database connection and query capabilities
    
    Args:
        database_name: Name of database connection to test (default: 'legacy')
        
    Returns:
        Dictionary with connection test results
    """
    result = {
        "database": database_name,
        "django_setup": False,
        "connection_available": False,
        "raw_query_works": False,
        "context_count": 0,
        "sample_contexts": [],
        "errors": []
    }
    
    try:
        # Setup Django
        if _setup_django():
            result["django_setup"] = True
        else:
            result["errors"].append("Django setup failed")
            return result
        
        # Test database connection
        from django.db import connections
        
        if database_name in connections:
            conn = connections[database_name]
            result["connection_available"] = True
            result["connection_settings"] = conn.settings_dict
            
            # Test raw SQL query
            with conn.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM agsearch_ascontext")
                count = cursor.fetchone()[0]
                result["context_count"] = count
                result["raw_query_works"] = True
                
                # Get sample contexts
                if count > 0:
                    cursor.execute("SELECT id, name, context_type FROM agsearch_ascontext LIMIT 5")
                    rows = cursor.fetchall()
                    result["sample_contexts"] = [
                        {"id": row[0], "name": row[1], "context_type": row[2]} 
                        for row in rows
                    ]
        else:
            result["errors"].append(f"Database '{database_name}' not found in connections")
            result["available_databases"] = list(connections.databases.keys())
            
    except Exception as e:
        result["errors"].append(f"Database test error: {str(e)}")
    
    return result


def debug_django_setup() -> Dict[str, Any]:
    """
    Verify Django configuration and app loading
    
    Returns:
        Dictionary with Django setup diagnostics
    """
    result = {
        "django_configured": False,
        "settings_module": None,
        "installed_apps": [],
        "database_connections": [],
        "legacy_app_loaded": False,
        "working_directory": os.getcwd(),
        "python_path": sys.path[:5],
        "errors": []
    }
    
    try:
        # Check Django configuration
        if settings.configured:
            result["django_configured"] = True
            result["settings_module"] = os.environ.get('DJANGO_SETTINGS_MODULE')
            result["installed_apps"] = list(settings.INSTALLED_APPS)
            result["database_connections"] = list(settings.DATABASES.keys())
            
            # Check if legacy_agsearch app is loaded
            if 'legacy_agsearch' in settings.INSTALLED_APPS:
                result["legacy_app_loaded"] = True
        else:
            # Try to setup Django
            if _setup_django():
                result["django_configured"] = True
                result["settings_module"] = os.environ.get('DJANGO_SETTINGS_MODULE')
                result["installed_apps"] = list(settings.INSTALLED_APPS)
                result["database_connections"] = list(settings.DATABASES.keys())
                result["legacy_app_loaded"] = 'legacy_agsearch' in settings.INSTALLED_APPS
            else:
                result["errors"].append("Failed to configure Django")
                
    except Exception as e:
        result["errors"].append(f"Django setup diagnostic error: {str(e)}")
    
    return result


def debug_sprite_function(function_name: str = 'list_contexts', **kwargs) -> Dict[str, Any]:
    """
    Test specific sprite function execution
    
    Args:
        function_name: Name of sprite function to test
        **kwargs: Arguments to pass to the sprite function
        
    Returns:
        Dictionary with function execution results
    """
    result = {
        "function_name": function_name,
        "arguments": kwargs,
        "django_setup": False,
        "function_available": False,
        "execution_successful": False,
        "result_type": None,
        "result_length": None,
        "result_sample": None,
        "errors": []
    }
    
    try:
        # Setup Django
        if _setup_django():
            result["django_setup"] = True
        else:
            result["errors"].append("Django setup failed")
            return result
        
        # Import the agsearch models sprite
        import spr_agf_agsearch_models as agsearch
        
        # Check if function exists
        if hasattr(agsearch, function_name):
            result["function_available"] = True
            func = getattr(agsearch, function_name)
            
            # Execute the function
            func_result = func(**kwargs)
            result["execution_successful"] = True
            result["result_type"] = type(func_result).__name__
            
            if isinstance(func_result, list):
                result["result_length"] = len(func_result)
                if func_result:
                    result["result_sample"] = func_result[0] if len(func_result) > 0 else None
            elif isinstance(func_result, dict):
                result["result_length"] = len(func_result)
                result["result_sample"] = func_result
            else:
                result["result_sample"] = str(func_result)[:200]
                
        else:
            result["errors"].append(f"Function '{function_name}' not found in spr_agf_agsearch_models")
            result["available_functions"] = [attr for attr in dir(agsearch) if not attr.startswith('_')]
            
    except Exception as e:
        result["errors"].append(f"Sprite function test error: {str(e)}")
        import traceback
        result["traceback"] = traceback.format_exc()
    
    return result


def debug_mcp_context() -> Dict[str, Any]:
    """
    Debug MCP execution context and environment
    
    Returns:
        Dictionary with MCP context diagnostics
    """
    result = {
        "working_directory": os.getcwd(),
        "python_executable": sys.executable,
        "python_version": sys.version,
        "python_path": sys.path[:10],
        "environment_vars": {},
        "process_id": os.getpid(),
        "django_setup": False,
        "sprite_import": False,
        "errors": []
    }
    
    try:
        # Capture relevant environment variables
        env_vars = ['DJANGO_SETTINGS_MODULE', 'PYTHONPATH', 'PATH']
        for var in env_vars:
            result["environment_vars"][var] = os.environ.get(var, 'NOT_SET')
        
        # Test Django setup
        if _setup_django():
            result["django_setup"] = True
        
        # Test sprite import
        try:
            import spr_agf_agsearch_models
            result["sprite_import"] = True
            result["sprite_functions"] = [attr for attr in dir(spr_agf_agsearch_models) if not attr.startswith('_')]
        except ImportError as e:
            result["errors"].append(f"Sprite import failed: {e}")
            
    except Exception as e:
        result["errors"].append(f"MCP context diagnostic error: {str(e)}")
    
    return result


# Test function
if __name__ == "__main__":
    """Test the debugger sprite when run directly"""
    print("Testing spr_agf_debugger sprite...")
    
    print("\n=== Database Connection Test ===")
    db_result = debug_database_connection()
    print(f"Database test result: {db_result}")
    
    print("\n=== Django Setup Test ===")
    django_result = debug_django_setup()
    print(f"Django setup result: {django_result}")
    
    print("\n=== Sprite Function Test ===")
    sprite_result = debug_sprite_function('list_contexts', limit=3)
    print(f"Sprite function result: {sprite_result}")
    
    print("\n=== MCP Context Test ===")
    mcp_result = debug_mcp_context()
    print(f"MCP context result: {mcp_result}")
    
    print("\nspr_agf_debugger test complete.")
