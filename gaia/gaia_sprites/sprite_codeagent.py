#!/usr/bin/env python3
"""
Sprite Code Agent

A code agent that can write Python algorithms involving sprite calls using MCPAdapt.
This agent has access to all Gaia sprites as tools and can write complex Python code
to loop, combine, and orchestrate sprite calls for sophisticated data analysis.

Based on:
- https://github.com/grll/mcpadapt
- https://grll.github.io/mcpadapt/
- smolagents CodeAgent framework

pip install smolagents[mcp]

"""

import sys
import os
import re
from datetime import datetime
from typing import List, Optional, Any

#MODEL="anthropic/claude-sonnet-4-20250514"
MODEL="anthropic/claude-opus-4-20250514"

def setup_error_monitoring():
    """Create errors directory and initialize error tracking files"""
    errors_dir = os.path.join(os.path.dirname(__file__), "errors")
    os.makedirs(errors_dir, exist_ok=True)
    return errors_dir

def log_unauthorized_import(module_name: str, error_message: str, errors_dir: str):
    """Log unauthorized import to deduped list, full log, and CSV with counts"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 1. Add to deduped UNAUTHORIZED_IMPORTS.txt (one per line, no duplicates)
    unauthorized_file = os.path.join(errors_dir, "UNAUTHORIZED_IMPORTS.txt")
    existing_modules = set()

    if os.path.exists(unauthorized_file):
        with open(unauthorized_file, 'r') as f:
            existing_modules = set(line.strip() for line in f if line.strip())

    if module_name not in existing_modules:
        with open(unauthorized_file, 'a') as f:
            f.write(f"{module_name}\n")
        print(f"[ERROR MONITOR] Added {module_name} to UNAUTHORIZED_IMPORTS.txt")

    # 2. Append to UNAUTHORIZED_IMPORTS.log with timestamp and full error
    unauthorized_log = os.path.join(errors_dir, "UNAUTHORIZED_IMPORTS.log")
    with open(unauthorized_log, 'a') as f:
        f.write(f"[{timestamp}] Module: {module_name}\n")
        f.write(f"Error: {error_message}\n")
        f.write("-" * 80 + "\n")

    # 3. Update UNAUTHORIZED_IMPORTS.csv with incremental counts
    update_unauthorized_imports_csv(module_name, timestamp, errors_dir)

def update_unauthorized_imports_csv(module_name: str, timestamp: str, errors_dir: str):
    """Update CSV file with unauthorized import counts"""
    csv_file = os.path.join(errors_dir, "UNAUTHORIZED_IMPORTS.csv")

    # Read existing data
    import_counts = {}
    first_seen = {}
    last_seen = {}

    if os.path.exists(csv_file):
        try:
            with open(csv_file, 'r') as f:
                lines = f.readlines()
                if len(lines) > 1:  # Skip header
                    for line in lines[1:]:
                        line = line.strip()
                        if line:
                            parts = line.split(',')
                            if len(parts) >= 4:
                                module = parts[0]
                                count = int(parts[1])
                                first = parts[2]
                                last = parts[3]
                                import_counts[module] = count
                                first_seen[module] = first
                                last_seen[module] = last
        except Exception as e:
            print(f"[ERROR MONITOR] Warning: Could not read CSV file: {e}")

    # Update counts
    if module_name in import_counts:
        import_counts[module_name] += 1
        last_seen[module_name] = timestamp
        print(f"[ERROR MONITOR] Updated {module_name} count to {import_counts[module_name]} in CSV")
    else:
        import_counts[module_name] = 1
        first_seen[module_name] = timestamp
        last_seen[module_name] = timestamp
        print(f"[ERROR MONITOR] Added {module_name} to CSV with count 1")

    # Write updated CSV
    try:
        with open(csv_file, 'w') as f:
            f.write("module_name,attempt_count,first_seen,last_seen\n")
            # Sort by count (descending) then by module name
            sorted_modules = sorted(import_counts.keys(), key=lambda x: (-import_counts[x], x))
            for module in sorted_modules:
                f.write(f"{module},{import_counts[module]},{first_seen[module]},{last_seen[module]}\n")
    except Exception as e:
        print(f"[ERROR MONITOR] Error writing CSV: {e}")

def log_general_error(error_message: str, error_type: str, errors_dir: str):
    """Log any error to ERRORS.log"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    errors_log = os.path.join(errors_dir, "ERRORS.log")

    with open(errors_log, 'a') as f:
        f.write(f"[{timestamp}] {error_type}: {error_message}\n")
        f.write("-" * 80 + "\n")

def scan_for_unauthorized_imports(output_text: str, errors_dir: str):
    """Scan execution output for unauthorized import errors and log them"""
    # Precise patterns to catch unauthorized import errors without false positives
    import_error_patterns = [
        r"Import of ([a-zA-Z_][a-zA-Z0-9_]*) is not allowed",  # Standard format: "Import of modulename is not allowed"
        r"InterpreterError: Import of ([a-zA-Z_][a-zA-Z0-9_]*) is not allowed",  # InterpreterError format
    ]

    detected_modules = set()  # Use set to avoid duplicates in single scan

    for pattern in import_error_patterns:
        matches = re.findall(pattern, output_text)
        for module_name in matches:
            if module_name and module_name not in detected_modules:
                detected_modules.add(module_name)
                log_unauthorized_import(module_name, f"Import of {module_name} is not allowed", errors_dir)
                print(f"[ERROR MONITOR] Detected unauthorized import: {module_name}")

    # Additional scan: Look for import statements in lines that contain "not allowed"
    # This catches cases where the error message format might be different
    lines = output_text.split('\n')
    for line in lines:
        if "not allowed" in line.lower():
            # Look for import statements in this specific line
            import_matches = re.findall(r"import\s+([a-zA-Z_][a-zA-Z0-9_]*)", line)
            for module_name in import_matches:
                # Skip known authorized imports
                authorized_imports = ['json', 'pandas', 'numpy', 'matplotlib', 'sklearn', 'scipy', 'requests', 'seaborn', 'plotly', 'csv', 'datetime', 're', 'time', 'math', 'collections', 'random', 'statistics', 'base64', 'hashlib', 'urllib', 'itertools', 'queue', 'stat', 'unicodedata']
                if module_name not in detected_modules and module_name not in authorized_imports:
                    detected_modules.add(module_name)
                    log_unauthorized_import(module_name, f"Unauthorized import detected in error context: {module_name}", errors_dir)
                    print(f"[ERROR MONITOR] Detected unauthorized import from error line: {module_name}")

def scan_for_all_errors(output_text: str, errors_dir: str):
    """Scan for any errors in the output and log them"""
    # Look for various error patterns
    error_patterns = [
        r"Code execution failed.*?due to: (.+)",
        r"Error: (.+)",
        r"Exception: (.+)",
        r"InterpreterError: (.+)",
        r"Warning to user: (.+)"
    ]

    for pattern in error_patterns:
        matches = re.findall(pattern, output_text, re.DOTALL)
        for error_msg in matches:
            # Clean up the error message
            clean_error = error_msg.strip().replace('\n', ' ')[:500]  # Limit length
            log_general_error(clean_error, "CodeAgent Execution Error", errors_dir)

def create_sprite_codeagent(
    model_id: str = MODEL,
    stream_outputs: bool = True,
    transport: str = "stdio",
    server_url: str = "http://localhost:8000"
):
    """
    Create a CodeAgent with access to all Gaia sprites as tools via MCP.

    Args:
        model_id: LLM model to use (default: Claude Sonnet 4)
        stream_outputs: Whether to stream outputs
        transport: Transport protocol - "stdio" or "http" (default: stdio)
        server_url: HTTP server URL if using http transport

    Returns:
        CodeAgent instance with all sprite tools loaded
    """
    try:
        # Import required libraries
        from smolagents import CodeAgent
        from mcp import ClientSession, StdioServerParameters
        from mcpadapt.core import MCPAdapt
        from mcpadapt.smolagents_adapter import SmolAgentsAdapter
        import httpx
        import asyncio

        # Ensure we have a proper event loop
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                print("Event loop is closed, creating new one...")
                asyncio.set_event_loop(asyncio.new_event_loop())
        except RuntimeError:
            print("No event loop found, creating new one...")
            asyncio.set_event_loop(asyncio.new_event_loop())

        # Try different model imports based on what's available
        model = None

        # Try LiteLLM first (supports most models)
        try:
            from smolagents import LiteLLMModel
            model = LiteLLMModel(model_id=model_id)
            print(f"✓ Using LiteLLM model: {model_id}")
        except ImportError:
            print("LiteLLM not available, trying InferenceClientModel...")

        # Fallback to InferenceClientModel
        if model is None:
            try:
                from smolagents import InferenceClientModel
                model = InferenceClientModel(model_id=model_id)
                print(f"✓ Using InferenceClient model: {model_id}")
            except ImportError:
                print("InferenceClientModel not available, trying TransformersModel...")

        # Last fallback to TransformersModel
        if model is None:
            try:
                from smolagents import TransformersModel
                model = TransformersModel(model_id=model_id)
                print(f"✓ Using Transformers model: {model_id}")
            except ImportError:
                raise ImportError("No compatible model backend found. Install smolagents[litellm] or smolagents[transformers]")

        # Configure MCP server connection based on transport
        if transport.lower() == "http":
            # HTTP transport - connect to existing server
            try:
                import httpx
                # Test if server is running
                response = httpx.get(f"{server_url}/health", timeout=2.0)
                if response.status_code == 200:
                    print(f"✓ Using HTTP transport: {server_url}")
                    # For HTTP, we'll need to implement HTTP-based tool loading
                    # This is a more complex implementation that would require
                    # custom HTTP client integration with MCPAdapt
                    raise NotImplementedError("HTTP transport not yet fully implemented. Use transport='stdio' for now.")
                else:
                    raise ConnectionError(f"HTTP server not responding at {server_url}")
            except Exception as e:
                print(f"✗ HTTP transport failed: {e}")
                print("Falling back to stdio transport...")
                transport = "stdio"

        if transport.lower() == "stdio":
            # Stdio transport - start our own server process
            current_dir = os.path.dirname(os.path.abspath(__file__))
            mcp_server_path = os.path.join(current_dir, "gaia_sprite_mcp", "sprite_mcp_server_level1.py")

            if not os.path.exists(mcp_server_path):
                raise FileNotFoundError(f"MCP server not found at: {mcp_server_path}")

            # Ensure Django settings are properly set for MCP subprocess
            mcp_env = os.environ.copy()
            mcp_env['DJANGO_SETTINGS_MODULE'] = 'gaia.djangaia.djangaia.settings'

            server_params = StdioServerParameters(
                command=sys.executable,  # Use current Python executable
                args=[mcp_server_path, "--transport", "stdio"],
                env=mcp_env  # Pass environment with Django settings
            )
            print("✓ Using Level 1 MCP server with stdio transport")
        else:
            raise ValueError(f"Unsupported transport: {transport}. Use 'stdio' or 'http'.")

        print("🔧 Loading Gaia sprites via MCPAdapt...")

        # Create a persistent MCP adapter that stays alive
        mcp_adapter = MCPAdapt(server_params, SmolAgentsAdapter())
        tools = mcp_adapter.__enter__()

        print(f"✓ Loaded {len(tools)} sprite tools:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")

        # Create the CodeAgent with all sprite tools and additional imports
        agent = CodeAgent(
            tools=tools,
            model=model,
            stream_outputs=stream_outputs,
            additional_authorized_imports=['json', 'urllib', 'base64', 'hashlib', 'csv', 'pandas', 'numpy', 'matplotlib', 'seaborn', 'plotly', 'requests', 'sklearn', 'scipy', 'io'],
            planning_interval=1,
        )

        # Store the adapter reference so it doesn't get garbage collected
        agent._mcp_adapter = mcp_adapter

        print("🤖 Sprite CodeAgent created successfully!")
        return agent

    except Exception as e:
        print(f"✗ Error creating sprite code agent: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_sprite_algorithm(
    query: str,
    model_id: str = MODEL,
    stream_outputs: bool = True,
    transport: str = "stdio",
    server_url: str = "http://localhost:8000"
):
    """
    One-liner to run a sprite algorithm using the code agent.

    Args:
        query: The task/algorithm to execute
        model_id: LLM model to use
        stream_outputs: Whether to stream outputs
        transport: Transport protocol - "stdio" or "http"
        server_url: HTTP server URL if using http transport

    Returns:
        Result from the agent execution
    """
    agent = None
    errors_dir = setup_error_monitoring()

    try:
        agent = create_sprite_codeagent(
            model_id=model_id,
            stream_outputs=stream_outputs,
            transport=transport,
            server_url=server_url
        )
        if agent is None:
            error_msg = "Failed to create sprite code agent"
            log_general_error(error_msg, "Agent Creation Error", errors_dir)
            return {"error": error_msg}

        print(f"🚀 Running sprite algorithm: {query}")
        print(f"🤖 Agent model: {model_id}")
        print(f"🔧 Available tools: {len(agent.tools) if hasattr(agent, 'tools') else 'Unknown'}")
        print(f"📝 Stream outputs: {stream_outputs}")
        print("=" * 80)

        # Capture stdout to monitor for errors
        import io
        from contextlib import redirect_stdout, redirect_stderr

        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()

        try:
            print("🎯 Starting agent execution...")
            print("💭 Agent will now analyze the query and decide which tools to use...")
            print("📊 Watch for tool calls and reasoning steps below:")
            print("=" * 80)

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                result = agent.run(query)

            print("=" * 80)
            print("✅ Agent execution completed!")
            print(f"📋 Final result type: {type(result)}")
            if isinstance(result, str):
                print(f"📋 Result preview: {result[:200]}...")
            print("=" * 80)
        except Exception as run_error:
            # If agent.run fails, we still want to capture any output
            result = {"error": f"Agent execution failed: {str(run_error)}"}
            log_general_error(str(run_error), "Agent Execution Error", errors_dir)

        # Get captured output
        stdout_output = stdout_capture.getvalue()
        stderr_output = stderr_capture.getvalue()

        # Print the captured output so user still sees it
        if stdout_output:
            print(stdout_output, end='')
        if stderr_output:
            print(stderr_output, end='', file=sys.stderr)

        # Scan all output for errors
        all_output = stdout_output + stderr_output + str(result)
        scan_for_unauthorized_imports(all_output, errors_dir)
        scan_for_all_errors(all_output, errors_dir)

        return {"result": result, "success": True}

    except Exception as e:
        error_msg = f"Error running sprite algorithm: {str(e)}"
        log_general_error(error_msg, "Algorithm Execution Error", errors_dir)
        return {"error": error_msg, "success": False}

    finally:
        # Clean up MCP adapter if it exists
        if agent and hasattr(agent, '_mcp_adapter'):
            try:
                agent._mcp_adapter.__exit__(None, None, None)
                print("✓ MCP adapter cleaned up")
            except Exception as cleanup_error:
                print(f"Warning: MCP cleanup failed: {cleanup_error}")

def list_sprite_tools():
    """
    List all available sprite tools that the CodeAgent can access.
    """
    print("🔧 Available Sprite Tools")
    print("=" * 60)

    try:
        # Create a temporary agent to get the tools list
        from smolagents import CodeAgent
        from mcp import ClientSession, StdioServerParameters
        from mcpadapt.core import MCPAdapt
        from mcpadapt.smolagents_adapter import SmolAgentsAdapter
        import asyncio

        # Ensure we have a proper event loop
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                asyncio.set_event_loop(asyncio.new_event_loop())
        except RuntimeError:
            asyncio.set_event_loop(asyncio.new_event_loop())

        # Configure MCP server connection
        current_dir = os.path.dirname(os.path.abspath(__file__))
        mcp_server_path = os.path.join(current_dir, "gaia_sprite_mcp", "sprite_mcp_server_level1.py")

        if not os.path.exists(mcp_server_path):
            raise FileNotFoundError(f"MCP server not found at: {mcp_server_path}")

        # Ensure Django settings are properly set for MCP subprocess
        mcp_env = os.environ.copy()
        mcp_env['DJANGO_SETTINGS_MODULE'] = 'gaia.djangaia.djangaia.settings'

        server_params = StdioServerParameters(
            command=sys.executable,  # Use current Python executable
            args=[mcp_server_path, "--transport", "stdio"],
            env=mcp_env  # Pass environment with Django settings
        )

        print("🔗 Connecting to sprite MCP server...")

        # Create a temporary MCP adapter to get tools
        with MCPAdapt(server_params, SmolAgentsAdapter()) as tools:
            print(f"✓ Found {len(tools)} available sprite tools:\n")

            # Group tools by category for better organization
            categories = {
                "Data Search": [],
                "Research & Analysis": [],
                "LLM Operations": [],
                "Utility": []
            }

            for tool in tools:
                name = tool.name
                desc = tool.description

                # Categorize tools
                if any(keyword in name.lower() for keyword in ['search', 'frame', 'investor']):
                    categories["Data Search"].append((name, desc))
                elif any(keyword in name.lower() for keyword in ['research', 'multimodel']):
                    categories["Research & Analysis"].append((name, desc))
                elif any(keyword in name.lower() for keyword in ['llm_', 'completion', 'analysis', 'generation']):
                    categories["LLM Operations"].append((name, desc))
                else:
                    categories["Utility"].append((name, desc))

            # Display tools by category
            for category, tool_list in categories.items():
                if tool_list:
                    print(f"📂 {category}")
                    print("-" * (len(category) + 4))
                    for name, desc in sorted(tool_list):
                        print(f"  • {name}")
                        print(f"    {desc}")
                        print()
                    print()

            print("💡 Usage Examples:")
            print("  python sprite_codeagent.py --query \"Find AgTech companies using agsearch\"")
            print("  python sprite_codeagent.py --query \"Research vertical farming using multiple tools\"")
            print("  python sprite_codeagent.py --query \"Create a market analysis combining web search and LLM analysis\"")

    except Exception as e:
        print(f"✗ Error listing tools: {e}")
        print("Make sure the MCP server is properly configured and dependencies are installed.")

def demo_sprite_algorithms():
    """
    Demo function showing various sprite algorithm examples.
    """
    print("🎯 Sprite CodeAgent Demo")
    print("=" * 50)

    # Create the agent once
    agent = create_sprite_codeagent()
    if agent is None:
        print("Failed to create agent")
        return

    # Demo algorithms
    demos = [
        {
            "name": "Multi-Source Research",
            "query": """
            Research 'vertical farming' using multiple sources:
            1. Search AgFunder database for vertical farming companies
            2. Get market research data for vertical farming
            3. Search external web for latest vertical farming news
            4. Combine all results into a comprehensive report
            """
        },
        {
            "name": "Investor Analysis",
            "query": """
            Analyze top AgTech investors:
            1. Search for investors focused on 'agriculture technology'
            2. Get detailed stats for the top 3 investors
            3. Find co-investment patterns between them
            4. Create a summary of their investment strategies
            """
        },
        {
            "name": "Data Frame Analysis",
            "query": """
            Analyze company data:
            1. List available data frames
            2. Search for companies in the 'sustainability' sector
            3. Get statistics about the frame
            4. Create insights about company distribution by country
            """
        },
        {
            "name": "Multi-Model Consensus",
            "query": """
            Get consensus on 'Future of AgTech':
            1. Query multiple LLM models about AgTech trends
            2. Compare their responses
            3. Find consensus points
            4. Generate a unified prediction
            """
        }
    ]

    for i, demo in enumerate(demos, 1):
        print(f"\n🔍 Demo {i}: {demo['name']}")
        print("-" * 30)

        try:
            result = agent.run(demo['query'])
            print(f"✓ Result: {result}")
        except Exception as e:
            print(f"✗ Error: {e}")

        if i < len(demos):
            input("\nPress Enter to continue to next demo...")

# Usage examples and documentation
USAGE_EXAMPLES = """
# Sprite CodeAgent Usage Examples

## Basic Usage
```python
from sprite_codeagent import create_sprite_codeagent, run_sprite_algorithm

# Create agent with stdio transport (default)
agent = create_sprite_codeagent()

# Create agent with HTTP transport (requires running server)
agent = create_sprite_codeagent(transport="http", server_url="http://localhost:8000")

# Run algorithm
result = agent.run("Find top 5 AgTech companies and their funding rounds")
```

## One-liner Usage
```python
# Stdio transport (default)
result = run_sprite_algorithm("Compare Tesla and Rivian using multiple data sources")

# HTTP transport
result = run_sprite_algorithm(
    "Compare Tesla and Rivian using multiple data sources",
    transport="http",
    server_url="http://localhost:8000"
)
```

## Advanced Multi-Step Algorithms
```python
agent = create_sprite_codeagent()

# Complex research workflow
result = agent.run('''
# Multi-source AgTech analysis
companies = agsearch("vertical farming", limit=10)
market_data = market_research_tool("vertical farming")
news = web_search_tool("vertical farming 2024", sources=["news"])

# Combine and analyze
for company in companies["results"][:5]:
    company_news = web_search_tool(f'{company["name"]} funding', sources=["news"])
    # Process and correlate data...

# Generate insights
insights = multimodel_query_tool("Analyze vertical farming market trends", models=["claude", "gpt-4"])
''')
```

## Available Sprite Tools
- agsearch: AgFunder company database
- market_research_tool: TAM/CAGR data
- web_search_tool: Multi-source web search
- news_search_tool: News search
- wiki_search_tool: Wikipedia search
- omni_search_tool: Internal omni search
- frame_search_tool: Data frame search
- frame_list_tool: List data frames
- investor_search_tool: Investor search
- investor_stats_tool: Investor analytics
- multimodel_query_tool: Multi-LLM queries
"""

if __name__ == "__main__":
    """
    Command line interface for the sprite code agent.
    """
    import argparse

    parser = argparse.ArgumentParser(description="Sprite CodeAgent - AI agent with access to all Gaia sprites")
    parser.add_argument("--demo", action="store_true", help="Run demo algorithms")
    parser.add_argument("--query", type=str, help="Query to execute")
    parser.add_argument("--model", type=str, default=MODEL, help="Model to use")
    parser.add_argument("--no-stream", action="store_true", help="Disable streaming outputs")
    parser.add_argument("--examples", action="store_true", help="Show usage examples")
    parser.add_argument("--list-tools", action="store_true", help="List all available sprite tools")

    args = parser.parse_args()

    if args.examples:
        print(USAGE_EXAMPLES)
    elif args.demo:
        demo_sprite_algorithms()
    elif args.list_tools:
        list_sprite_tools()
    elif args.query:
        result = run_sprite_algorithm(
            query=args.query,
            model_id=args.model,
            stream_outputs=not args.no_stream
        )
        print(f"Result: {result}")
    else:
        print("Sprite CodeAgent")
        print("Usage:")
        print("  python sprite_codeagent.py --demo")
        print("  python sprite_codeagent.py --query 'Find top AgTech companies'")
        print("  python sprite_codeagent.py --list-tools")
        print("  python sprite_codeagent.py --examples")
        print("  python sprite_codeagent.py --help")

