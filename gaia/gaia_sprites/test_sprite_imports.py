#!/usr/bin/env python3
"""
Test script to debug sprite imports in MCP server context
"""

import sys
import os

print("=== Debugging Sprite Imports ===")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")
print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

# Add parent directory to path for sprite imports (same as MCP server)
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
    print(f"Added to path: {parent_dir}")

print("\n=== Testing Individual Sprite Imports ===")

def test_import(module_name, func_name=None):
    try:
        if func_name:
            module = __import__(module_name, fromlist=[func_name])
            func = getattr(module, func_name)
            print(f"✅ {module_name}.{func_name}")
            return True
        else:
            __import__(module_name)
            print(f"✅ {module_name}")
            return True
    except Exception as e:
        print(f"❌ {module_name}{('.' + func_name) if func_name else ''}: {e}")
        return False

# Test all sprites - comprehensive list
sprites_to_test = [
    # Core AgFunder sprites
    ('spr_agf_agsearch', 'quick_search'),
    ('spr_agf_agsearch_models', 'list_contexts'),
    ('spr_agf_agsearch_models', 'search_contexts'),
    ('spr_agf_agsearch_models', 'fetch_context'),
    ('spr_agf_debugger', 'debug_database_connection'),
    ('spr_agf_debugger', 'debug_django_setup'),
    ('spr_agf_frames', 'frame_search'),
    ('spr_agf_frames', 'frame_list'),
    ('spr_agf_investors', 'investor_search'),
    ('spr_agf_investors', 'investor_stats'),
    ('spr_agf_multimodel', 'multimodel_query'),
    ('spr_agf_multimodel', 'multimodel_fast'),
    ('spr_agf_omnisearch', 'omni_search'),
    ('spr_agf_omnisearch', 'semantic_search'),
    ('spr_agf_sql_agent', 'sql_query'),
    ('spr_agf_sql_agent', 'list_databases'),
    ('spr_agf_sql_agent', 'quick_sql'),

    # External/utility sprites
    ('spr_ext_babyfetch', 'babyfetch_url'),
    ('spr_ext_babyfetch_simple', 'babyfetch_simple'),
    ('spr_ext_deep_research', 'deep_research_simple'),
    ('spr_ext_search', 'web_search'),
    ('spr_ext_search', 'news_search'),
    ('spr_ext_search', 'business_news'),
    ('spr_ext_wiki', 'wiki_search'),

    # System sprites
    ('spr_elf_mem', 'elf_mem_store'),
    ('spr_elf_mem', 'elf_mem_get'),
    ('spr_llm_smart', 'llm_completion_text'),
    ('spr_llm_smart', 'llm_completion_json'),
    ('spr_llm_smart', 'llm_analyze_code'),
    ('spr_llm_smart', 'llm_generate_code'),
    ('spr_llm_smart', 'llm_explain_concept'),
    ('spr_llm_smart', 'llm_debug_error'),
    ('spr_simple_chart', 'simple_chart_svg'),
    ('spr_tamarind', 'market_research'),
]

success_count = 0
for module_name, func_name in sprites_to_test:
    if test_import(module_name, func_name):
        success_count += 1

print(f"\n=== Results ===")
print(f"Successfully imported: {success_count}/{len(sprites_to_test)}")

if success_count == len(sprites_to_test):
    print("🎉 All sprite imports successful!")
    
    # Test the exact import pattern from MCP server
    print("\n=== Testing MCP Server Import Pattern ===")
    try:
        # Core AgFunder sprites
        from spr_agf_agsearch import quick_search
        from spr_agf_agsearch_models import list_contexts, search_contexts, fetch_context
        from spr_agf_debugger import debug_database_connection, debug_django_setup
        from spr_agf_frames import frame_search, frame_list
        from spr_agf_investors import investor_search, investor_stats
        from spr_agf_multimodel import multimodel_query, multimodel_fast
        from spr_agf_omnisearch import omni_search, semantic_search
        from spr_agf_sql_agent import sql_query, list_databases, quick_sql

        # External/utility sprites
        from spr_ext_babyfetch import babyfetch_url
        from spr_ext_babyfetch_simple import babyfetch_simple
        from spr_ext_deep_research import deep_research_simple
        from spr_ext_search import web_search, news_search, business_news
        from spr_ext_wiki import wiki_search

        # System sprites
        from spr_elf_mem import elf_mem_store, elf_mem_get
        from spr_llm_smart import (
            llm_completion_text, llm_completion_json, llm_analyze_code,
            llm_generate_code, llm_explain_concept, llm_debug_error
        )
        from spr_simple_chart import simple_chart_svg
        from spr_tamarind import market_research


        sprites = {
            # Core AgFunder sprites
            'quick_search': quick_search,
            'list_contexts': list_contexts,
            'search_contexts': search_contexts,
            'fetch_context': fetch_context,
            'debug_database_connection': debug_database_connection,
            'debug_django_setup': debug_django_setup,
            'frame_search': frame_search,
            'frame_list': frame_list,
            'investor_search': investor_search,
            'investor_stats': investor_stats,
            'multimodel_query': multimodel_query,
            'multimodel_fast': multimodel_fast,
            'omni_search': omni_search,
            'semantic_search': semantic_search,
            'sql_query': sql_query,
            'list_databases': list_databases,
            'quick_sql': quick_sql,

            # External/utility sprites
            'babyfetch_url': babyfetch_url,
            'babyfetch_simple': babyfetch_simple,
            'deep_research_simple': deep_research_simple,
            'web_search': web_search,
            'news_search': news_search,
            'business_news': business_news,
            'wiki_search': wiki_search,

            # System sprites
            'elf_mem_store': elf_mem_store,
            'elf_mem_get': elf_mem_get,
            'llm_completion_text': llm_completion_text,
            'llm_completion_json': llm_completion_json,
            'llm_analyze_code': llm_analyze_code,
            'llm_generate_code': llm_generate_code,
            'llm_explain_concept': llm_explain_concept,
            'llm_debug_error': llm_debug_error,
            'simple_chart_svg': simple_chart_svg,
            'market_research': market_research,
        }
        
        print(f"✅ MCP Server pattern successful! Loaded {len(sprites)} sprite functions")
        
    except ImportError as e:
        print(f"❌ MCP Server pattern failed: {e}")
else:
    print("❌ Some imports failed - check individual results above")
