#!/usr/bin/env python3
"""
Sprite MCP Server Level 1

Level 1 MCP server with basic sprite tools only.
Provides all 11 core Gaia sprites as MCP tools.
"""

import sys
import os

# Environment variables should now be inherited from parent process

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sprite_mcp_server_base import SpriteMCPServerBase

class SpriteMCPServerLevel1(SpriteMCPServerBase):
    """Level 1 MCP server with basic sprite tools"""
    
    def __init__(self):
        super().__init__("sprite-mcp-server-level1")
    
    def _init_sprites(self):
        """Initialize basic sprite imports"""
        sprites = self._safe_import_sprites()
        if sprites:
            self.sprites_available = True
            self.sprites = sprites
            print(f"✓ Level 1: Loaded {len(sprites)} basic sprites", file=sys.stderr)
        else:
            self.sprites_available = False
            self.sprites = {}
            print("✗ Level 1: No sprites available", file=sys.stderr)
    
    def _register_tools(self):
        """Register basic sprite tools"""
        if self.sprites_available:
            self._register_basic_tools(self.sprites)
            print(f"✓ Level 1: Registered {len(self.tool_registry)} tools", file=sys.stderr)
        else:
            # Register placeholder tool
            @self.mcp.tool()
            def sprites_unavailable() -> dict:
                """Placeholder tool when sprites are not available"""
                return {"error": "Sprites not available - missing dependencies"}
            
            self.tool_registry = {
                "sprites_unavailable": {
                    "name": "sprites_unavailable", 
                    "description": "Placeholder tool when sprites are not available"
                }
            }

if __name__ == "__main__":
    SpriteMCPServerLevel1.main()
