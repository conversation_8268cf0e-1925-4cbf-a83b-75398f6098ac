# -*- coding: utf-8 -*-
"""
Custom PostgreSQL backend that bypasses version checking for legacy databases
"""

from django.db.backends.postgresql import base


class DatabaseWrapper(base.DatabaseWrapper):
    """Custom PostgreSQL backend that skips version checking"""

    def check_database_version_supported(self):
        """Override to skip PostgreSQL version checking for legacy databases"""
        # Skip the version check entirely for legacy databases
        # This allows PostgreSQL 10.2 to work with Django 5.1.7
        pass
