# -*- coding: utf-8 -*-
"""
Utility functions for working with legacy Django 1.11 models.
Provides simple query/read and add operations for AsContext and AsRating.
"""

from django.contrib.auth.models import User
from .models import LegacyAsContext, LegacyAsRating


class LegacyAsContextManager:
    """Manager for legacy AsContext operations"""
    
    @staticmethod
    def get_all():
        """Get all AsContext records from legacy database"""
        return LegacyAsContext.objects.using('legacy').all()
    
    @staticmethod
    def get_by_id(context_id):
        """Get AsContext by ID from legacy database"""
        try:
            return LegacyAsContext.objects.using('legacy').get(id=context_id)
        except LegacyAsContext.DoesNotExist:
            return None
    
    @staticmethod
    def get_by_name(name):
        """Get AsContext by name from legacy database"""
        return LegacyAsContext.objects.using('legacy').filter(name__icontains=name)
    
    @staticmethod
    def get_by_type(context_type):
        """Get As<PERSON>ontext by type from legacy database"""
        return LegacyAsContext.objects.using('legacy').filter(context_type=context_type)
    
    @staticmethod
    def get_by_user(user_id):
        """Get AsContext by created_by user from legacy database"""
        return LegacyAsContext.objects.using('legacy').filter(created_by_id=user_id)
    
    @staticmethod
    def create(name, context_type='draft', created_by_id=None, keywords=None, comments=None, tagtree_id=None):
        """
        Create new AsContext in legacy database
        
        Args:
            name (str): Context name
            context_type (str): One of 'draft', 'tag', 'client', 'compete', 'junk'
            created_by_id (int): User ID who created this context
            keywords (str): Keywords text
            comments (str): Comments text
            tagtree_id (int): TagTree ID if applicable
            
        Returns:
            LegacyAsContext: Created context object
        """
        context = LegacyAsContext(
            name=name,
            context_type=context_type,
            keywords=keywords,
            comments=comments,
            tagtree_id=tagtree_id
        )
        
        if created_by_id:
            try:
                # Verify user exists
                user = User.objects.get(id=created_by_id)
                context.created_by = user
            except User.DoesNotExist:
                pass  # Leave created_by as None
        
        context.save(using='legacy')
        return context


class LegacyAsRatingManager:
    """Manager for legacy AsRating operations"""
    
    @staticmethod
    def get_all():
        """Get all AsRating records from legacy database"""
        return LegacyAsRating.objects.using('legacy').all()
    
    @staticmethod
    def get_by_context(context_id):
        """Get AsRating records by context ID from legacy database"""
        return LegacyAsRating.objects.using('legacy').filter(context_id=context_id)
    
    @staticmethod
    def get_by_es_id(es_id):
        """Get AsRating records by Elasticsearch ID from legacy database"""
        return LegacyAsRating.objects.using('legacy').filter(es_id=es_id)
    
    @staticmethod
    def get_by_user(user_id):
        """Get AsRating records by created_by user from legacy database"""
        return LegacyAsRating.objects.using('legacy').filter(created_by_id=user_id)
    
    @staticmethod
    def get_by_rating(rating_value):
        """Get AsRating records by rating value from legacy database"""
        return LegacyAsRating.objects.using('legacy').filter(rating=rating_value)
    
    @staticmethod
    def create(es_id, context_id, rating=0, created_by_id=None, es_idx=None, goid=None):
        """
        Create new AsRating in legacy database
        
        Args:
            es_id (str): Elasticsearch document ID
            context_id (int): AsContext ID this rating belongs to
            rating (int): Rating value (typically -1, 0, or 1)
            created_by_id (int): User ID who created this rating
            es_idx (str): Elasticsearch index name
            goid (str): Additional identifier
            
        Returns:
            LegacyAsRating: Created rating object
        """
        # Verify context exists
        try:
            context = LegacyAsContext.objects.using('legacy').get(id=context_id)
        except LegacyAsContext.DoesNotExist:
            raise ValueError(f"AsContext with ID {context_id} does not exist")
        
        rating_obj = LegacyAsRating(
            es_id=es_id,
            context=context,
            rating=rating,
            es_idx=es_idx or 'blank',
            goid=goid
        )
        
        if created_by_id:
            try:
                # Verify user exists
                user = User.objects.get(id=created_by_id)
                rating_obj.created_by = user
            except User.DoesNotExist:
                pass  # Leave created_by as None
        
        rating_obj.save(using='legacy')
        return rating_obj
    
    @staticmethod
    def update_rating(rating_id, new_rating):
        """Update rating value for existing AsRating"""
        try:
            rating_obj = LegacyAsRating.objects.using('legacy').get(id=rating_id)
            rating_obj.rating = new_rating
            rating_obj.save(using='legacy')
            return rating_obj
        except LegacyAsRating.DoesNotExist:
            return None


# Convenience functions for quick access
def get_contexts():
    """Quick function to get all contexts"""
    return LegacyAsContextManager.get_all()

def get_context(context_id):
    """Quick function to get context by ID"""
    return LegacyAsContextManager.get_by_id(context_id)

def create_context(name, context_type='draft', **kwargs):
    """Quick function to create context"""
    return LegacyAsContextManager.create(name, context_type, **kwargs)

def get_ratings_for_context(context_id):
    """Quick function to get ratings for a context"""
    return LegacyAsRatingManager.get_by_context(context_id)

def create_rating(es_id, context_id, rating=0, **kwargs):
    """Quick function to create rating"""
    return LegacyAsRatingManager.create(es_id, context_id, rating, **kwargs)
