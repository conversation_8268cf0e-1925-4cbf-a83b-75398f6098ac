# -*- coding: utf-8 -*-
"""
Database router to direct legacy models to the legacy database
"""


class LegacyRouter:
    """
    A router to control all database operations on legacy models
    """
    
    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        if model._meta.app_label == 'legacy_agsearch':
            return 'legacy'
        return None

    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        if model._meta.app_label == 'legacy_agsearch':
            return 'legacy'
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations if models are in the same app."""
        db_set = {'default', 'legacy'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that legacy models are not migrated."""
        if app_label == 'legacy_agsearch':
            return db == 'legacy'
        elif db == 'legacy':
            return False
        return None
