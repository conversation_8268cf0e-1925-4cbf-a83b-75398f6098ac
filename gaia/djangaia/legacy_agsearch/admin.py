from django.contrib import admin
from .models import LegacyAsContext, LegacyAsRating, LegacyAsDoc

# Register your models here.

@admin.register(LegacyAsContext)
class LegacyAsContextAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'context_type', 'created_by', 'created_at', 'tagtree_id')
    list_filter = ('context_type', 'created_by')
    search_fields = ['name']
    readonly_fields = ('created_at',)


@admin.register(LegacyAsRating)
class LegacyAsRatingAdmin(admin.ModelAdmin):
    list_display = ('id', 'es_id', 'es_idx', 'rating', 'context', 'created_by')
    list_filter = ('context', 'rating', 'es_idx', 'created_by')
    search_fields = ['es_id', 'context__name']


@admin.register(LegacyAsDoc)
class LegacyAsDocAdmin(admin.ModelAdmin):
    list_display = ('id', 'index', 'key', 'created_at')
    list_filter = ('index',)
    search_fields = ['key']
