from django.test import TestCase
from django.contrib.auth.models import User
from .models import LegacyAsContext, LegacyAsRating
from .utils import (
    LegacyAsContextManager, 
    LegacyAsRatingManager,
    get_contexts,
    get_context,
    create_context,
    get_ratings_for_context,
    create_rating
)

# Create your tests here.

class LegacyAsContextTests(TestCase):
    """Tests for legacy AsContext functionality"""
    
    def test_get_all_contexts(self):
        """Test getting all contexts"""
        contexts = get_contexts()
        self.assertIsNotNone(contexts)
    
    def test_context_manager_methods(self):
        """Test context manager methods"""
        # Test get_all
        contexts = LegacyAsContextManager.get_all()
        self.assertIsNotNone(contexts)
        
        # Test get_by_type
        draft_contexts = LegacyAsContextManager.get_by_type('draft')
        self.assertIsNotNone(draft_contexts)


class LegacyAsRatingTests(TestCase):
    """Tests for legacy AsRating functionality"""
    
    def test_get_all_ratings(self):
        """Test getting all ratings"""
        ratings = LegacyAsRatingManager.get_all()
        self.assertIsNotNone(ratings)
    
    def test_rating_manager_methods(self):
        """Test rating manager methods"""
        # Test get_all
        ratings = LegacyAsRatingManager.get_all()
        self.assertIsNotNone(ratings)
        
        # Test get_by_rating
        positive_ratings = LegacyAsRatingManager.get_by_rating(1)
        self.assertIsNotNone(positive_ratings)
