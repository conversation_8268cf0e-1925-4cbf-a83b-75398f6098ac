# -*- coding: utf-8 -*-
"""
Legacy Django 1.11 models for accessing the agsearch database.
These models mirror the Django 1.11 structure but are compatible with Django 3.2.
"""

from django.db import models
from django.contrib.auth.models import User
from django.contrib.postgres.fields import J<PERSON><PERSON>ield


# Choices from the original models
CONTEXT_TYPE = (
    ('draft', 'draft'),
    ('tag', 'tag'),
    ('client', 'client'),
    ('compete', 'compete'),
    ('junk', 'junk'),
)

SEEK_TYPE = (
    ('mtl', 'mtl'),  # ES more like this
    ('sem-mtl', 'sem-mtl'),  # semantic more like this
    ('tagmath', 'tagmath'),  # look for matching tags formulas
    ('none', 'none'),
)


class LegacyAsDoc(models.Model):
    """Legacy AsDoc model for Django 1.11 database"""
    index = models.CharField(max_length=100)
    key = models.Char<PERSON><PERSON>(max_length=300)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        app_label = 'legacy_agsearch'
        managed = False  # Don't let Django manage this table
        db_table = 'agsearch_asdoc'  # Actual table name in Django 1.11 db


class LegacyAsContext(models.Model):
    """Legacy AsContext model for Django 1.11 database"""
    name = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User, 
        blank=True, 
        null=True,
        on_delete=models.SET_NULL,  # Django 3.2 requires on_delete
        db_column='created_by_id'  # Explicit column name
    )
    
    # Note: We'll handle TagTree and AisClient relationships separately
    # since they may not exist in your Django 3.2 app
    tagtree_id = models.IntegerField(blank=True, null=True)
    
    context_type = models.CharField(
        choices=CONTEXT_TYPE, 
        default='draft', 
        max_length=10, 
        unique=False
    )
    
    keywords = models.TextField(max_length=1000, blank=True, null=True)
    comments = models.TextField(max_length=1000, blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        app_label = 'legacy_agsearch'
        managed = False  # Don't let Django manage this table
        db_table = 'agsearch_ascontext'  # Actual table name in Django 1.11 db
        ordering = ['context_type', 'name']


class LegacyAsRating(models.Model):
    """Legacy AsRating model for Django 1.11 database"""
    es_id = models.CharField(max_length=200, default='blank')
    es_idx = models.CharField(max_length=200, blank=True, null=True)
    
    rating = models.IntegerField(default=0)
    context = models.ForeignKey(
        LegacyAsContext, 
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        db_column='context_id'  # Explicit column name
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User, 
        blank=True, 
        null=True, 
        on_delete=models.SET_NULL,
        db_column='created_by_id'  # Explicit column name
    )
    
    goid = models.CharField(max_length=200, blank=True, null=True)

    def bound_rating(self, vote):
        """
        Bound self.rating to -1, 0 or 1.
        :param vote:
        :return: int
        """
        if vote > 0 and self.rating > 0:
            return 0
        if vote < 0 and self.rating < 0:
            return 0
        if vote > 0:
            return 1
        if vote < 0:
            return -1
        return 0

    class Meta:
        app_label = 'legacy_agsearch'
        managed = False  # Don't let Django manage this table
        db_table = 'agsearch_asrating'  # Actual table name in Django 1.11 db
