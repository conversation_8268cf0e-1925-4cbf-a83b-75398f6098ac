#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to demonstrate legacy Django 1.11 model access from Django 3.2 app.
Run this from the Django 3.2 app directory with Django environment loaded.

Usage:
    python manage.py shell
    exec(open('test_legacy_access.py').read())
"""

import os
import sys
import django

# Setup Django environment if not already done
if not hasattr(django.conf.settings, 'configured') or not django.conf.settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangaia.settings')
    django.setup()

from legacy_agsearch.utils import (
    LegacyAsContextManager,
    LegacyAsRatingManager,
    get_contexts,
    get_context,
    create_context,
    get_ratings_for_context,
    create_rating
)

def test_legacy_access():
    """Test legacy database access"""
    print("=== Testing Legacy Django 1.11 Database Access ===\n")
    
    # Test 1: Query existing contexts
    print("1. Querying existing AsContext records...")
    try:
        contexts = get_contexts()
        print(f"   Found {contexts.count()} contexts")
        
        # Show first few contexts
        for context in contexts[:5]:
            print(f"   - ID: {context.id}, Name: {context.name}, Type: {context.context_type}")
    except Exception as e:
        print(f"   Error querying contexts: {e}")
    
    print()
    
    # Test 2: Query contexts by type
    print("2. Querying contexts by type...")
    try:
        draft_contexts = LegacyAsContextManager.get_by_type('draft')
        print(f"   Found {draft_contexts.count()} draft contexts")
    except Exception as e:
        print(f"   Error querying draft contexts: {e}")
    
    print()
    
    # Test 3: Query existing ratings
    print("3. Querying existing AsRating records...")
    try:
        ratings = LegacyAsRatingManager.get_all()
        print(f"   Found {ratings.count()} ratings")
        
        # Show first few ratings
        for rating in ratings[:5]:
            print(f"   - ID: {rating.id}, ES_ID: {rating.es_id}, Rating: {rating.rating}, Context: {rating.context_id}")
    except Exception as e:
        print(f"   Error querying ratings: {e}")
    
    print()
    
    # Test 4: Create new context (commented out to avoid creating test data)
    print("4. Testing context creation (dry run)...")
    try:
        # Uncomment the next line to actually create a test context
        # new_context = create_context("Test Context from Django 3.2", "draft", keywords="test django 3.2")
        # print(f"   Created context: {new_context.id} - {new_context.name}")
        print("   Context creation test skipped (uncomment to run)")
    except Exception as e:
        print(f"   Error creating context: {e}")
    
    print()
    
    # Test 5: Create new rating (commented out to avoid creating test data)
    print("5. Testing rating creation (dry run)...")
    try:
        # First get a context to rate against
        contexts = get_contexts()
        if contexts.exists():
            context_id = contexts.first().id
            # Uncomment the next line to actually create a test rating
            # new_rating = create_rating("test_es_id_123", context_id, rating=1)
            # print(f"   Created rating: {new_rating.id} for context {context_id}")
            print(f"   Rating creation test skipped (would use context {context_id})")
        else:
            print("   No contexts available for rating test")
    except Exception as e:
        print(f"   Error creating rating: {e}")
    
    print()
    print("=== Legacy Database Access Test Complete ===")

def show_context_details(context_id):
    """Show detailed information about a specific context"""
    context = get_context(context_id)
    if not context:
        print(f"Context {context_id} not found")
        return
    
    print(f"Context Details for ID {context_id}:")
    print(f"  Name: {context.name}")
    print(f"  Type: {context.context_type}")
    print(f"  Created: {context.created_at}")
    print(f"  Created by: {context.created_by_id}")
    print(f"  Keywords: {context.keywords}")
    print(f"  Comments: {context.comments}")
    print(f"  TagTree ID: {context.tagtree_id}")
    
    # Show ratings for this context
    ratings = get_ratings_for_context(context_id)
    print(f"  Ratings: {ratings.count()} total")
    for rating in ratings[:10]:  # Show first 10
        print(f"    - Rating: {rating.rating}, ES_ID: {rating.es_id}")

if __name__ == "__main__":
    test_legacy_access()
    
    # Example of showing details for a specific context
    # show_context_details(1)  # Replace 1 with actual context ID
