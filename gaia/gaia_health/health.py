import requests
import learn.simpletrans.xform_flask_client
import learn.ml_org_dataset_flask_client
import gaia.gaia_ml.family.fam_hf.sbert.sbert_flask_client as sbert_client
from learn.simpletrans import xform_base
from gaia.gaia_elasticsearch.gaia_elasticsearch import GaiaElastico
from gaia.gaia_health.base import GaiaHealth, GaiaHealthDjangaia



class GaiaHealthCoresignal(GaiaHealthDjangaia):

    def __init__(self):
        self.name = 'coresignal browser'
        self.endpoint = 'https://agbase.agfunder.com:444/dealer_cs/'
        self.validation_string = 'dealer_coresignal'


class GaiaHealthSuperset(GaiaHealthDjangaia):

    def __init__(self):
        self.name = 'apache superset browser'
        self.endpoint = 'https://agbase.agfunder.com:570/'
        self.validation_string = 'Dashboards'


class GaiaHealthGaiachat(GaiaHealthDjangaia):

    def __init__(self):
        self.name = 'gaiachat'
        self.endpoint = 'https://agbase.agfunder.com:580/'
        self.validation_string = 'GaiaChat'


class GaiaHealthESIDX(GaiaHealth):
    def __init__(self,  alias='opensearch_worker_fast', idx='idx_cb10_fresh1'):
        self.alias = alias
        self.idx = idx
        self.name = f'{self.alias}: {self.idx}'
        super().__init__()

    def check(self):
        print('check health....')
        elastico = GaiaElastico()

        try:
            elastico.connect(alias=self.alias)
        except Exception as e:
            return 'Connection Error', {'error': e}
        latest_record = elastico.latest_record(index=self.idx)
        context = {'latest_record': latest_record['date'].strftime("%d-%m-%Y, %H:%M:%S")}
        return 'ok', context


class GaiaHealthOpensearch(GaiaHealth):

    def __init__(self,  cluster_alias='agsearch'):
        self.name = f'opensearch: {cluster_alias}'
        self.endpoint = 'https://agbase.agfunder.com/search/'
        self.cluster_map = {
            'agsearch': {
                'cluster_alias': 'opensearch_worker_fast',
                'index_alias': 'agsearch_cb',
            }
        }
        self.cluster_props = self.cluster_map.get(cluster_alias)
        self.name = f'{self.cluster_props["cluster_alias"]}: {self.cluster_props["index_alias"]}'

    def check(self):
        print('check health....')
        elastico = GaiaElastico()

        try:
            elastico.connect(alias=self.cluster_props['cluster_alias'])
            live_index = list(elastico.es.indices.get(self.cluster_props['index_alias']))[0]
        except Exception as e:
            return 'Connection Error', {'error': e}
        latest_record = elastico.latest_record(index=live_index)
        context = {'live_index': live_index, 'latest_record': latest_record['date'].strftime("%d-%m-%Y, %H:%M:%S")}
        return 'ok', context


class GaiaHealthAgsearchMoreLike(GaiaHealthDjangaia):

    def __init__(self):
        self.name = 'agsearch.more_like'
        self.endpoint = 'https://agbase.agfunder.com/search/?context_id=10&logical=&logical_negative=&mode=more-like-sem&xform_incl_flag=&xform_incl_strict=&funding_round=&webtext=&created_after=&founded_after=&funding_latest=&sort=&custom_filter='


#  GPU models
class GaiaHealthModel(GaiaHealth):

    def check(self):
        params = {'q00000': 'hello world'}
        try:
            res = requests.post(self.endpoint, params)
            if res.status_code != 200:
                return 500, res.reason
            res_json = res.json
        except Exception as e:
            print('GaiaHealth Model Error: ', e)
            return 500, 'endpoint error'

        context = {'input': params, 'result': res.json()}
        return 'ok', context



class GaiaHealthXform(GaiaHealthModel):

    def __init__(self):
        self.name = 'learn.simpletrans.xform_flask_client'
        self.endpoint = f'http://127.0.0.1:{xform_base.port_num}/xform/classif/'


class GaiaHealthMLorg(GaiaHealthModel):

    def __init__(self):
        self.name = 'learn.ml_org_dataset_flask_client.get_org_text_predictions_multi'
        self.endpoint = f'http://127.0.0.1:5250/org_ml_nlp_text'


class GaiaHealthSbert(GaiaHealthModel):

    def __init__(self):
        self.name = 'gaia.gaia_ml.family.fam_hf.sbert.sbert_flask_client'
        self.endpoint = 'http://127.0.0.1:18102/embed/sbert_embed/EMB_02_xlm/'


class GaiaHealthKNN(GaiaHealthModel):

    def __init__(self):
        self.name = 'learn.sbert.sbert_flask_client.embeds'
        self.endpoint = 'http://127.0.0.1:18100/embed/sbert_embed/'

