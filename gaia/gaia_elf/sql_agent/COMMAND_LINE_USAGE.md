# Command Line Usage Guide

This guide shows how to run SQL queries from the command line using the CrunchBase SQL Agent.

## Quick Start

### Method 1: Direct Python Script
```bash
# Navigate to the sql_agent directory
cd gaia/gaia_elf/sql_agent

# Run a simple query using the agent directly
python -c "
from agent_cb import CrunchBaseSQLAgent
agent = CrunchBaseSQLAgent()
result = agent.query('Count rounds for facebook.com', execute=True)
print('SQL:', result['sql_generation']['sql_query'])
print('Results:', result['sql_execution']['results'])
"
```

### Method 2: Using the CLI Script
```bash
# Run a single query
python cli.py "Count rounds for facebook.com"

# Generate SQL only (don't execute)
python cli.py "Find investors in California" --no-execute

# Output as JSON
python cli.py "Show companies founded after 2020" --format json

# Output SQL only
python cli.py "List all Series A rounds" --format sql

# Interactive mode
python cli.py --interactive

# Use different model
python cli.py "Count total organizations" --model gpt-4o-mini

# Enable debug output
python cli.py "Find AI companies" --debug
```

## Sample Queries

### Basic Counting Queries
```bash
python cli.py "Count rounds for facebook.com"
python cli.py "How many organizations are there?"
python cli.py "Count investors from California"
```

### Company Information
```bash
python cli.py "Find companies founded in San Francisco"
python cli.py "Show companies that raised more than 1 million"
python cli.py "List companies in the AI category"
```

### Investor Queries
```bash
python cli.py "Find all investors from New York"
python cli.py "Show investors who invested in Series A rounds"
python cli.py "List top investors by investment count"
```

### Funding Round Analysis
```bash
python cli.py "Show all Series A funding rounds"
python cli.py "Find rounds with more than 10 million raised"
python cli.py "List recent funding rounds from 2020"
```

## Output Formats

### Human-Readable (Default)
```bash
python cli.py "Count rounds for facebook.com"
```
Output:
```
Query: Count rounds for facebook.com
============================================================
Generated SQL:
SELECT COUNT(*) as funding_rounds_count 
FROM django.cb16_funding_rounds fr
JOIN django.cb16_organizations org ON fr.company_uuid = org.uuid
WHERE org.domain = 'facebook.com'

Explanation: This query counts funding rounds for Facebook by joining...
Tables used: django.cb16_funding_rounds, django.cb16_organizations
✅ Execution successful
Results (1 rows):
  1: {'funding_rounds_count': 4}
```

### JSON Format
```bash
python cli.py "Count rounds for facebook.com" --format json
```
Output:
```json
{
  "user_query": "Count rounds for facebook.com",
  "sql_generation": {
    "sql_query": "SELECT COUNT(*) as funding_rounds_count...",
    "explanation": "This query counts funding rounds...",
    "tables_used": ["django.cb16_funding_rounds", "django.cb16_organizations"]
  },
  "sql_execution": {
    "success": true,
    "results": [{"funding_rounds_count": 4}]
  }
}
```

### SQL Only Format
```bash
python cli.py "Count rounds for facebook.com" --format sql
```
Output:
```sql
SELECT COUNT(*) as funding_rounds_count 
FROM django.cb16_funding_rounds fr
JOIN django.cb16_organizations org ON fr.company_uuid = org.uuid
WHERE org.domain = 'facebook.com'
```

## Advanced Usage

### Generate SQL Without Execution
```bash
# Useful for reviewing SQL before execution
python cli.py "Delete all records" --no-execute
```

### Using Different Models
```bash
# Use GPT-4o-mini instead of Claude Sonnet 4
python cli.py "Find investors" --model gpt-4o-mini

# Use GPT-3.5-turbo
python cli.py "Count companies" --model gpt-3.5-turbo
```

### Debug Mode
```bash
# See detailed LLM interactions and SQL execution steps
python cli.py "Find companies" --debug
```

### Interactive Mode
```bash
# Start interactive session
python cli.py --interactive
```

In interactive mode:
```
CB Query> Count rounds for facebook.com
CB Query> Find investors in California  
CB Query> help
CB Query> quit
```

## Direct Database Connection

You can also connect directly to the database using standard PostgreSQL tools:

```bash
# Using psql
PGPASSWORD=password psql -h localhost -p 15432 -U postgres agbase1

# Then run the generated SQL
agbase1=# SELECT COUNT(*) FROM django.cb16_funding_rounds fr 
          JOIN django.cb16_organizations org ON fr.company_uuid = org.uuid 
          WHERE org.domain = 'facebook.com';
```

## Environment Variables

Override database connection settings:
```bash
export CB_DB_HOST=localhost
export CB_DB_PORT=15432
export CB_DB_NAME=agbase1
export CB_DB_USER=postgres
export CB_DB_PASSWORD=password

python cli.py "Count organizations"
```

## Error Handling

The CLI provides helpful error messages:

```bash
# Connection error
python cli.py "Count companies"
# Output: ❌ Error: Database connection failed: connection refused

# SQL generation error  
python cli.py "Invalid query that makes no sense"
# Output: ❌ Failed to generate SQL: Error: Could not understand query

# SQL execution error
python cli.py "SELECT * FROM nonexistent_table" --no-execute
# Shows the SQL but doesn't execute it
```

## Integration with Shell Scripts

```bash
#!/bin/bash
# Example shell script using the SQL agent

echo "Getting Facebook funding data..."
RESULT=$(python cli.py "Count rounds for facebook.com" --format json)
echo $RESULT | jq '.sql_execution.results[0].funding_rounds_count'

echo "Getting total organizations..."
python cli.py "Count total organizations" --format sql > query.sql
psql -h localhost -p 15432 -U postgres agbase1 -f query.sql
```

## Tips

1. **Use quotes** around your natural language queries
2. **Start simple** with basic counting queries
3. **Use --no-execute** to review SQL before running
4. **Try --format sql** to get just the SQL for use elsewhere
5. **Use --debug** to understand what's happening
6. **Interactive mode** is great for exploration
