#!/usr/bin/env python3
"""
Test that terminology notes are properly included in the CrunchBase context.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

def test_terminology_in_context():
    """Test that terminology clarifications are included in the context."""
    print("=== Testing Terminology Notes in Context ===\n")
    
    try:
        from dbs.cb.context import get_context
        
        context = get_context()
        rules_content = context['rules']
        tables_content = context['tables']
        
        # Check for terminology notes in rules
        terminology_keywords = [
            'round code',
            'round type', 
            'funding type',
            'investment_type',
            'TERMINOLOGY NOTES'
        ]
        
        found_in_rules = []
        for keyword in terminology_keywords:
            if keyword in rules_content:
                found_in_rules.append(keyword)
        
        print(f"✅ Found {len(found_in_rules)}/{len(terminology_keywords)} terminology keywords in rules")
        print(f"Found in rules: {found_in_rules}")
        
        # Check for terminology notes in investment types section
        if 'round code' in tables_content and 'investment_type column' in tables_content:
            print("✅ Terminology clarification found in investment types section")
        else:
            print("❌ Terminology clarification NOT found in investment types section")
        
        # Check specific clarifications
        clarifications = [
            'round code',
            'round type', 
            'funding type',
            'investment_type column'
        ]
        
        found_clarifications = []
        combined_content = rules_content + tables_content
        for clarification in clarifications:
            if clarification in combined_content:
                found_clarifications.append(clarification)
        
        print(f"✅ Found {len(found_clarifications)}/{len(clarifications)} clarifications in context")
        
        return len(found_clarifications) >= 3  # At least 3 out of 4 should be found
        
    except Exception as e:
        print(f"❌ Error testing terminology: {e}")
        return False

def show_terminology_section():
    """Show the terminology section from the context."""
    print("\n=== Terminology Section Content ===\n")
    
    try:
        from dbs.cb.context import get_context
        
        context = get_context()
        rules_content = context['rules']
        
        # Find and show terminology section
        lines = rules_content.split('\n')
        in_terminology_section = False
        terminology_lines = []
        
        for line in lines:
            if 'TERMINOLOGY NOTES' in line:
                in_terminology_section = True
                terminology_lines.append(line)
            elif in_terminology_section:
                if line.strip() and line.startswith('###') and 'TERMINOLOGY' not in line:
                    break  # Next section started
                terminology_lines.append(line)
        
        if terminology_lines:
            print("Terminology Notes Section:")
            for line in terminology_lines:
                print(line)
        else:
            print("❌ No terminology section found")
        
        return len(terminology_lines) > 0
        
    except Exception as e:
        print(f"❌ Error showing terminology section: {e}")
        return False

def test_example_queries():
    """Test example queries that would benefit from terminology clarification."""
    print("\n=== Example Query Scenarios ===\n")
    
    example_queries = [
        "Find all Series A round codes",
        "Show companies by round type", 
        "Count funding types in 2020",
        "List all round codes available"
    ]
    
    print("These queries should now be correctly interpreted as referring to investment_type:")
    for i, query in enumerate(example_queries, 1):
        print(f"{i}. \"{query}\"")
        print(f"   → Should query investment_type column")
    
    print("\nWith the terminology notes, the SQL agent will understand that:")
    print("• 'round code' = investment_type column")
    print("• 'round type' = investment_type column") 
    print("• 'funding type' = investment_type column")
    print("• Values like 'series_a', 'seed', 'angel' are in investment_type")
    
    return True

def main():
    """Run all tests."""
    print("=== Terminology Clarification Test ===\n")
    
    tests = [
        ("Terminology in Context", test_terminology_in_context),
        ("Terminology Section", show_terminology_section),
        ("Example Query Scenarios", test_example_queries)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n=== Test Results ===")
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed!")
        print("✅ Terminology clarifications are properly included in context")
        print("✅ SQL agent will now correctly interpret 'round code/type' as investment_type")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
