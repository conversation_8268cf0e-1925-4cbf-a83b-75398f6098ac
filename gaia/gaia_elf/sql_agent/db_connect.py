#!/usr/bin/env python3
"""
General Database Connection Utilities

This module contains generic database connection utilities that can be used
with any database configuration. Database-specific configs should only contain
the minimal connection data.
"""

import os
from typing import Dict, Any, Optional


def apply_env_overrides(config: Dict[str, Any], env_mapping: Dict[str, str]) -> Dict[str, Any]:
    """
    Apply environment variable overrides to a configuration dictionary.
    
    Args:
        config: Base configuration dictionary
        env_mapping: Mapping of config keys to environment variable names
        
    Returns:
        Updated configuration dictionary
    """
    updated_config = config.copy()
    
    for config_key, env_var in env_mapping.items():
        env_value = os.getenv(env_var)
        if env_value:
            # Convert port to int if it's the port setting
            if config_key == 'port':
                try:
                    updated_config[config_key] = int(env_value)
                except ValueError:
                    print(f"Warning: Invalid port value in {env_var}: {env_value}")
            else:
                updated_config[config_key] = env_value
    
    return updated_config


def get_db_config(default_config: Dict[str, Any], 
                  env_mapping: Optional[Dict[str, str]] = None,
                  override_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get database configuration with priority order:
    1. override_config parameter
    2. Environment variables (if env_mapping provided)
    3. Default configuration
    
    Args:
        default_config: Default database configuration
        env_mapping: Optional mapping of config keys to environment variables
        override_config: Optional configuration dictionary to override defaults
        
    Returns:
        Complete database configuration dictionary
    """
    config = default_config.copy()
    
    # Apply environment variable overrides if mapping provided
    if env_mapping:
        config = apply_env_overrides(config, env_mapping)
    
    # Apply explicit overrides
    if override_config:
        config.update(override_config)
    
    return config


def get_connection_string(config: Dict[str, Any]) -> str:
    """
    Generate a PostgreSQL connection string from configuration.
    
    Args:
        config: Database configuration dictionary
        
    Returns:
        PostgreSQL connection string
    """
    conn_parts = []
    
    if config.get('host'):
        conn_parts.append(f"host={config['host']}")
    if config.get('port'):
        conn_parts.append(f"port={config['port']}")
    if config.get('database'):
        conn_parts.append(f"dbname={config['database']}")
    if config.get('user'):
        conn_parts.append(f"user={config['user']}")
    if config.get('password'):
        conn_parts.append(f"password={config['password']}")
    
    return " ".join(conn_parts)


def get_psql_command(config: Dict[str, Any]) -> str:
    """
    Generate a psql command line for connecting to the database.
    
    Args:
        config: Database configuration dictionary
        
    Returns:
        psql command string
    """
    cmd_parts = ["psql"]
    
    if config.get('host'):
        cmd_parts.extend(["-h", config['host']])
    if config.get('port'):
        cmd_parts.extend(["-p", str(config['port'])])
    if config.get('user'):
        cmd_parts.extend(["-U", config['user']])
    if config.get('database'):
        cmd_parts.append(config['database'])
    
    return " ".join(cmd_parts)


def test_connection_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Test a database configuration by attempting to connect.
    
    Args:
        config: Database configuration dictionary
        
    Returns:
        Dictionary with test results
    """
    try:
        import psycopg2
        
        # Attempt connection
        conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            database=config['database'],
            user=config['user'],
            password=config.get('password')
        )
        
        # Test basic query
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return {
            'success': True,
            'message': 'Connection successful',
            'config': config,
            'database_version': version
        }
        
    except ImportError:
        return {
            'success': False,
            'message': 'psycopg2 not available',
            'config': config,
            'database_version': None
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Connection failed: {str(e)}',
            'config': config,
            'database_version': None
        }


def print_connection_info(config: Dict[str, Any], env_mapping: Optional[Dict[str, str]] = None):
    """
    Print connection information for debugging.
    
    Args:
        config: Database configuration dictionary
        env_mapping: Optional environment variable mapping
    """
    print("=== Database Connection Info ===")
    print(f"Type: {config.get('type', 'Unknown')}")
    print(f"Host: {config['host']}")
    print(f"Port: {config['port']}")
    print(f"Database: {config['database']}")
    print(f"User: {config['user']}")
    print(f"Schema: {config.get('schema', 'N/A')}")
    print(f"Password: {'***' if config.get('password') else 'Not set'}")
    
    print(f"\nConnection String: {get_connection_string(config)}")
    print(f"psql Command: {get_psql_command(config)}")
    
    # Show environment variable options if mapping provided
    if env_mapping:
        print(f"\nEnvironment Variables (for override):")
        for config_key, env_var in env_mapping.items():
            current_value = os.getenv(env_var, 'Not set')
            print(f"  {env_var}: {current_value}")


if __name__ == "__main__":
    # Example usage
    example_config = {
        'type': 'postgresql',
        'host': 'localhost',
        'port': 5432,
        'database': 'testdb',
        'user': 'testuser',
        'password': 'testpass'
    }
    
    example_env_mapping = {
        'host': 'DB_HOST',
        'port': 'DB_PORT',
        'database': 'DB_NAME',
        'user': 'DB_USER',
        'password': 'DB_PASSWORD'
    }
    
    print("=== General Database Connection Utilities ===")
    print_connection_info(example_config, example_env_mapping)
