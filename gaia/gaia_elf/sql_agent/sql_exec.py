#!/usr/bin/env python3
"""
Generic SQL Execution Module

This module provides database-agnostic SQL execution functionality.
It can work with any database that has a Python DB-API 2.0 compatible driver.
"""

import psycopg2
import psycopg2.extras
from contextlib import contextmanager
from typing import Dict, Any, Optional, List
import json


class SQLExecutor:
    """
    Generic SQL executor that can execute SQL queries against various databases.
    Currently supports PostgreSQL via psycopg2.
    """
    
    def __init__(self, db_config: Dict[str, Any], debug: bool = False):
        """
        Initialize the SQL Executor with database configuration.
        
        Args:
            db_config: Database configuration dictionary with connection parameters
            debug: Enable debug output
        """
        self.db_config = db_config
        self.debug = debug
        self.db_type = db_config.get('type', 'postgresql')
    
    @contextmanager
    def get_db_connection(self):
        """
        Context manager for database connections.
        
        Yields:
            Database connection object
        """
        conn = None
        try:
            if self.db_type.lower() == 'postgresql':
                conn = psycopg2.connect(
                    host=self.db_config['host'],
                    port=self.db_config['port'],
                    database=self.db_config['database'],
                    user=self.db_config['user'],
                    password=self.db_config.get('password')
                )
            else:
                raise ValueError(f"Unsupported database type: {self.db_type}")
                
            yield conn
            
        except Exception as e:
            if self.debug:
                print(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_sql(self, sql_query: str, fetch_results: bool = True, 
                   params: Optional[tuple] = None) -> Dict:
        """
        Execute a SQL query against the database.
        
        Args:
            sql_query: The SQL query to execute
            fetch_results: Whether to fetch and return results
            params: Optional parameters for parameterized queries
            
        Returns:
            Dictionary with execution results and metadata
        """
        try:
            with self.get_db_connection() as conn:
                if self.db_type.lower() == 'postgresql':
                    cursor_factory = psycopg2.extras.RealDictCursor
                else:
                    cursor_factory = None
                
                with conn.cursor(cursor_factory=cursor_factory) as cursor:
                    if self.debug:
                        print(f"Executing SQL: {sql_query}")
                        if params:
                            print(f"Parameters: {params}")
                    
                    cursor.execute(sql_query, params)
                    
                    result = {
                        'success': True,
                        'query': sql_query,
                        'rowcount': cursor.rowcount,
                        'description': cursor.description,
                        'results': None,
                        'error': None
                    }
                    
                    if fetch_results and cursor.description:
                        results = cursor.fetchall()
                        # Convert database-specific row objects to regular dicts
                        if hasattr(results[0] if results else None, '_asdict'):
                            # Named tuple-like objects
                            result['results'] = [row._asdict() for row in results]
                        elif hasattr(results[0] if results else None, 'keys'):
                            # Dict-like objects (RealDictCursor)
                            result['results'] = [dict(row) for row in results]
                        else:
                            # Regular tuples
                            columns = [desc[0] for desc in cursor.description]
                            result['results'] = [dict(zip(columns, row)) for row in results]
                        
                        result['result_count'] = len(results)
                    
                    conn.commit()
                    return result
                    
        except Exception as e:
            error_result = {
                'success': False,
                'query': sql_query,
                'error': str(e),
                'results': None,
                'rowcount': 0
            }
            if self.debug:
                print(f"SQL execution error: {e}")
            return error_result
    
    def execute_multiple(self, sql_queries: List[str], 
                        fetch_results: bool = True) -> List[Dict]:
        """
        Execute multiple SQL queries in sequence.
        
        Args:
            sql_queries: List of SQL queries to execute
            fetch_results: Whether to fetch and return results
            
        Returns:
            List of result dictionaries, one for each query
        """
        results = []
        for query in sql_queries:
            result = self.execute_sql(query, fetch_results)
            results.append(result)
            # Stop on first error
            if not result['success']:
                break
        return results
    
    def test_connection(self) -> Dict:
        """
        Test the database connection.
        
        Returns:
            Dictionary with connection test results
        """
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 as test")
                    result = cursor.fetchone()
                    
                    return {
                        'success': True,
                        'message': 'Connection successful',
                        'test_result': result,
                        'db_type': self.db_type
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'message': f'Connection failed: {str(e)}',
                'test_result': None,
                'db_type': self.db_type
            }
    
    def get_table_info(self, schema: Optional[str] = None) -> Dict:
        """
        Get information about tables in the database.
        
        Args:
            schema: Optional schema name to filter tables
            
        Returns:
            Dictionary with table information
        """
        if self.db_type.lower() == 'postgresql':
            if schema:
                query = """
                SELECT table_name, table_type 
                FROM information_schema.tables 
                WHERE table_schema = %s
                ORDER BY table_name
                """
                params = (schema,)
            else:
                query = """
                SELECT table_schema, table_name, table_type 
                FROM information_schema.tables 
                WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
                ORDER BY table_schema, table_name
                """
                params = None
        else:
            raise ValueError(f"Table info not implemented for {self.db_type}")
        
        return self.execute_sql(query, fetch_results=True, params=params)


def test_sql_executor():
    """Test function for the SQL executor"""
    # Sample configuration
    db_config = {
        'type': 'postgresql',
        'host': 'localhost',
        'port': 15432,
        'database': 'agbase1',
        'user': 'postgres',
        'password': 'password'
    }
    
    executor = SQLExecutor(db_config, debug=True)
    
    # Test connection
    conn_test = executor.test_connection()
    print("Connection test:", conn_test)
    
    # Test simple query
    if conn_test['success']:
        result = executor.execute_sql("SELECT 1 as test_value")
        print("Query result:", result)


if __name__ == "__main__":
    test_sql_executor()
