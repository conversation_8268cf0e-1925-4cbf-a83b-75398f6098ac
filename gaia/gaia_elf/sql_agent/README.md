# SQL Agent

An intelligent SQL query generator that uses gaia_llm completion_json to convert natural language queries into PostgreSQL SQL statements based on database schema context.

## Overview

The SQL Agent takes natural language queries and generates corresponding SQL queries by:
1. Using database table schemas and relationship information as context
2. Creating comprehensive prompts for the LLM
3. Leveraging gaia_llm's completion_json method for structured responses
4. Returning SQL queries with explanations and metadata

## Features

- **Natural Language to SQL**: Convert plain English queries to PostgreSQL SQL
- **Schema-Aware**: Uses actual database table schemas and relationships
- **Structured Output**: Returns JSON with SQL query, explanation, tables used, and assumptions
- **Error Handling**: Graceful error handling with informative messages
- **Caching**: Leverages gaia_llm's caching for improved performance
- **Debug Mode**: Optional verbose output for development and troubleshooting

## Database Schema

The agent works with the following tables:
- `cb16_organizations`: Company information including domain, location, funding details
- `cb16_funding_rounds`: Funding round details linked to organizations
- `cb16_investments`: Individual investment records linking rounds and investors
- `cb16_investors`: Investor information including location and type

### Table Relationships
```
cb16_funding_rounds → cb16_organizations (company_uuid → uuid)
cb16_investments → cb16_funding_rounds (funding_round_uuid → funding_round_uuid)
cb16_investments → cb16_investors (investor_uuid → uuid)
```

## Usage

### Basic Usage

```python
from gaia.gaia_elf.sql_agent.sql_agent import SQLAgent

# Initialize the agent
agent = SQLAgent(model="anthropic/claude-sonnet-4-20250514", debug=False)

# Generate SQL from natural language
result = agent.execute_query_request("Count rounds for facebook.com")

print("Generated SQL:", result['sql_query'])
print("Explanation:", result['explanation'])
print("Tables used:", result['tables_used'])
```

### Example Queries

- "Count rounds for facebook.com"
- "Find all investors from California"
- "Show companies that raised more than $1M"
- "List all Series A funding rounds"
- "Find investors who invested in AI companies"
- "Show companies founded in San Francisco"

### Response Format

The agent returns a dictionary with the following structure:

```json
{
    "sql_query": "SELECT COUNT(*) FROM cb16_funding_rounds...",
    "explanation": "Brief explanation of what the query does",
    "tables_used": ["cb16_funding_rounds", "cb16_organizations"],
    "assumptions": [
        "Assumption 1 about data interpretation",
        "Assumption 2 about query logic"
    ]
}
```

## API Reference

### SQLAgent Class

#### Constructor
```python
SQLAgent(model: str = "anthropic/claude-sonnet-4-20250514", debug: bool = False)
```

**Parameters:**
- `model`: LLM model to use (default: "anthropic/claude-sonnet-4-20250514")
- `debug`: Enable debug output (default: False)

#### Methods

##### execute_query_request
```python
execute_query_request(user_query: str, 
                     context_tables: Optional[str] = None,
                     context_joins: Optional[str] = None) -> Dict
```

Main method to process natural language queries and generate SQL.

**Parameters:**
- `user_query`: Natural language query string
- `context_tables`: Optional override for table schema context
- `context_joins`: Optional override for table relationships context

**Returns:** Dictionary with SQL query and metadata

##### generate_sql
```python
generate_sql(context_tables: str, context_joins: str, user_query: str) -> Dict
```

Core method that generates SQL based on context and query.

##### create_context_string
```python
create_context_string(context_tables: str, context_joins: str) -> str
```

Creates formatted context string for the LLM.

##### create_prompt
```python
create_prompt(context_string: str, user_query: str) -> str
```

Creates the complete prompt for SQL generation.

## Examples

### Running Examples

```bash
# Run basic examples
python example_usage.py

# Run in interactive mode
python example_usage.py --interactive

# Run tests
python test_sql_agent.py
```

### Sample Output

```
Query: Count rounds for facebook.com
Generated SQL:
SELECT COUNT(*) as funding_rounds_count 
FROM cb16_funding_rounds fr
JOIN cb16_organizations org ON fr.company_uuid = org.uuid
WHERE org.domain ILIKE '%facebook.com%'

Explanation: This query counts the number of funding rounds for companies with 'facebook.com' in their domain.
Tables used: cb16_funding_rounds, cb16_organizations
```

## Configuration

The agent uses the following configuration:
- **Model**: Configurable LLM model (default: gpt-4o-mini)
- **Caching**: Enabled via gaia_llm's Json_LlmClient_Cached
- **Database**: PostgreSQL syntax and functions
- **Schema**: All columns are text type, requiring appropriate casting

## Error Handling

The agent handles errors gracefully:
- LLM API errors return structured error responses
- Invalid queries return explanatory error messages
- Missing context falls back to module-level defaults

## Dependencies

- `gaia.gaia_llm`: For LLM completion_json functionality
- `typing`: For type hints
- `json`: For JSON processing

## Notes

- All database columns are of type 'text', requiring appropriate string operations
- Uses ILIKE for case-insensitive pattern matching
- Generates PostgreSQL-compatible SQL syntax
- Leverages table relationships for proper JOINs
- Includes assumptions and explanations for transparency
