#!/usr/bin/env python3
"""
Database Agent Factory

General-purpose factory for creating SQL agents for any database
discovered in the dbs/ directory structure.
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from sql_gen import SQLGenerator
from sql_exec import SQLExecutor


def discover_databases():
    """
    Automatically discover available databases from dbs/ directory structure.
    
    Returns:
        Dictionary of database configurations
    """
    databases = {}
    dbs_dir = os.path.join(os.path.dirname(__file__), 'dbs')
    
    if not os.path.exists(dbs_dir):
        return databases
    
    for db_name in os.listdir(dbs_dir):
        db_path = os.path.join(dbs_dir, db_name)
        
        # Skip if not a directory
        if not os.path.isdir(db_path):
            continue
            
        # Skip __pycache__ and other special directories
        if db_name.startswith('__'):
            continue
        
        # Check for required files
        context_file = os.path.join(db_path, 'context.py')
        connect_file = os.path.join(db_path, 'connect.py')
        
        if os.path.exists(context_file) and os.path.exists(connect_file):
            # Try to load the modules to get metadata
            try:
                context_module_name = f'dbs.{db_name}.context'
                connect_module_name = f'dbs.{db_name}.connect'

                # Import modules
                context_module = __import__(context_module_name, fromlist=[''])
                connect_module = __import__(connect_module_name, fromlist=[''])
                
                # Get database info from context if available
                db_info = {
                    'name': db_name.upper(),
                    'description': f'{db_name.upper()} database',
                    'context_module': context_module_name,
                    'connect_module': connect_module_name,
                    'context': context_module,
                    'connect': connect_module
                }
                
                # Try to get more descriptive info from the module docstring
                if hasattr(context_module, '__doc__') and context_module.__doc__:
                    lines = context_module.__doc__.strip().split('\n')
                    if len(lines) > 1:
                        db_info['description'] = lines[1].strip()
                
                # Try to get database type from context
                if hasattr(context_module, 'DATABASE_TYPE'):
                    db_info['database_type'] = context_module.DATABASE_TYPE
                
                # Try to get schema name
                if hasattr(context_module, 'SCHEMA_NAME'):
                    db_info['schema_name'] = context_module.SCHEMA_NAME
                
                databases[db_name] = db_info
                
            except Exception as e:
                print(f"Warning: Could not load database '{db_name}': {e}")
                continue
    
    return databases


def get_available_databases():
    """Get list of available databases (cached)."""
    if not hasattr(get_available_databases, '_cache'):
        get_available_databases._cache = discover_databases()
    return get_available_databases._cache


def create_agent(database, model=None, debug=False):
    """
    Create a generic agent for any discovered database.
    
    Args:
        database: Database identifier (any discovered database)
        model: LLM model to use
        debug: Enable debug output
        
    Returns:
        Database agent instance
    """
    databases = get_available_databases()
    
    if database not in databases:
        raise ValueError(f"Unknown database '{database}'. Available: {list(databases.keys())}")
    
    db_info = databases[database]
    
    # Get context and connection modules
    context_module = db_info['context']
    connect_module = db_info['connect']
    
    # Get context
    context = context_module.get_context()
    
    # Get database configuration - try common function names
    db_config = None
    for func_name in [f'get_{database}_config', f'get_{database}_db_config', 'get_db_config']:
        if hasattr(connect_module, func_name):
            db_config = getattr(connect_module, func_name)()
            break
    
    if db_config is None:
        # Try to find any function that returns a config
        for attr_name in dir(connect_module):
            if attr_name.startswith('get_') and attr_name.endswith('_config') and callable(getattr(connect_module, attr_name)):
                db_config = getattr(connect_module, attr_name)()
                break
    
    if db_config is None:
        raise ValueError(f"Could not find database configuration function in {db_info['connect_module']}")
    
    # Create components
    sql_generator = SQLGenerator(
        model=model or "anthropic/claude-sonnet-4-20250514",
        debug=debug
    )
    sql_executor = SQLExecutor(db_config, debug=debug)
    
    # Create a generic agent
    class GenericAgent:
        def __init__(self, sql_gen, sql_exec, db_name, db_info, context):
            self.sql_generator = sql_gen
            self.sql_executor = sql_exec
            self.database_name = db_name
            self.database_info = db_info
            self.context = context
            
        def test_connection(self):
            return self.sql_executor.test_connection()
            
        def query(self, query, execute=True):
            # Generate SQL
            sql_result = self.sql_generator.generate_sql(
                context_rules=self.context['rules'],
                context_tables=self.context['tables'],
                context_joins=self.context['joins'],
                user_query=query,
                database_type=self.context.get('database_type', 'PostgreSQL')
            )

            result = {
                'user_query': query,
                'sql_generation': sql_result
            }

            # Execute if requested
            if execute and sql_result.get('sql_query'):
                exec_result = self.sql_executor.execute_sql(sql_result['sql_query'])
                result['sql_execution'] = exec_result

            return result

        def generate_sql(self, query):
            """Generate SQL without executing."""
            return self.sql_generator.generate_sql(
                context_rules=self.context['rules'],
                context_tables=self.context['tables'],
                context_joins=self.context['joins'],
                user_query=query,
                database_type=self.context.get('database_type', 'PostgreSQL')
            )
            
        def execute_sql(self, sql):
            """Execute SQL directly."""
            return self.sql_executor.execute_sql(sql)
            
        def get_database_info(self):
            """Get database information."""
            return self.database_info
    
    return GenericAgent(sql_generator, sql_executor, database, db_info, context)


def list_databases():
    """List all available databases."""
    databases = get_available_databases()
    
    if not databases:
        print("No databases found in dbs/ directory")
        return
    
    print("Available Databases:")
    print("=" * 50)
    for db_id, db_info in databases.items():
        print(f"{db_id:12} - {db_info['description']}")
        if 'database_type' in db_info:
            print(f"{'':12}   Type: {db_info['database_type']}")
        if 'schema_name' in db_info:
            print(f"{'':12}   Schema: {db_info['schema_name']}")


def main():
    """Example usage of the database agent factory."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database Agent Factory")
    parser.add_argument('--list', action='store_true', help='List available databases')
    parser.add_argument('--test', help='Test connection to specified database')
    
    args = parser.parse_args()
    
    if args.list:
        list_databases()
        return
    
    if args.test:
        try:
            agent = create_agent(args.test)
            result = agent.test_connection()
            if result['success']:
                print(f"✅ Connection to {args.test} successful")
                print(f"Database version: {result.get('database_version', 'Unknown')}")
            else:
                print(f"❌ Connection to {args.test} failed: {result['message']}")
        except Exception as e:
            print(f"❌ Error testing {args.test}: {e}")
        return
    
    # Default: list databases
    list_databases()


if __name__ == "__main__":
    main()
