# General Purpose SQL Agent System

The SQL agent system is now completely general purpose and automatically discovers databases from the `dbs/` directory structure. No hardcoded database names!

## How It Works

### Automatic Database Discovery

The system automatically scans the `dbs/` directory and discovers any database that has:
- `dbs/{database_name}/context.py` - Database context and schema information
- `dbs/{database_name}/connect.py` - Database connection configuration

### Current Discovered Databases

```bash
# List all available databases
python cli.py --list-databases
```

Output:
```
Available Databases:
==================================================
agbasedb     - AGBASEDB Database Context
               Type: postgresql
cb           - CrunchBase Database Context  
               Type: PostgreSQL
```

## Command Line Usage

### Basic Queries

```bash
# Query default database (first discovered)
python cli.py "Count organizations"

# Query specific database
python cli.py "Count organizations" --database cb
python cli.py "Find AgTech companies" --database agbasedb

# Generate SQL only (don't execute)
python cli.py "Find investors" --database cb --no-execute

# Different output formats
python cli.py "Count companies" --database cb --format json
python cli.py "List funding rounds" --database agbasedb --format sql
```

### Interactive Mode

```bash
# Interactive mode with specific database
python cli.py --interactive --database cb
python cli.py --interactive --database agbasedb

# Interactive mode with debug
python cli.py --interactive --database cb --debug
```

### Database Management

```bash
# List available databases
python cli.py --list-databases

# Test database connection
python db_agent_factory.py --test cb
python db_agent_factory.py --test agbasedb
```

## Adding New Databases

To add a new database, simply create the directory structure:

```
dbs/
├── mynewdb/
│   ├── __init__.py
│   ├── connect.py
│   └── context.py
```

### 1. Create Connection Module (`dbs/mynewdb/connect.py`)

```python
#!/usr/bin/env python3
"""
MyNewDB Database Connection Configuration
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_connect import get_db_config, test_connection_config, print_connection_info

# Default connection configuration
DEFAULT_MYNEWDB_CONFIG = {
    'type': 'postgresql',  # or 'mysql', 'sqlite', etc.
    'host': 'localhost',
    'port': 5432,
    'database': 'mynewdb',
    'user': 'postgres',
    'password': 'password',
    'schema': 'public'
}

# Environment variable mappings
ENV_VAR_MAPPING = {
    'host': 'MYNEWDB_DB_HOST',
    'port': 'MYNEWDB_DB_PORT', 
    'database': 'MYNEWDB_DB_NAME',
    'user': 'MYNEWDB_DB_USER',
    'password': 'MYNEWDB_DB_PASSWORD',
    'schema': 'MYNEWDB_DB_SCHEMA'
}

def get_mynewdb_config(override_config=None):
    """Get MyNewDB database configuration."""
    return get_db_config(DEFAULT_MYNEWDB_CONFIG, ENV_VAR_MAPPING, override_config)

if __name__ == "__main__":
    config = get_mynewdb_config()
    print_connection_info(config, ENV_VAR_MAPPING)
```

### 2. Create Context Module (`dbs/mynewdb/context.py`)

```python
#!/usr/bin/env python3
"""
MyNewDB Database Context

Database context for MyNewDB with table schemas and relationships.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_context import get_context, extract_table_names, format_context_summary

# Database-specific rules
CONTEXT_RULES = """
### RULES
- Use appropriate SQL syntax for your database type
- Use proper JOINs based on foreign key relationships
- Return valid SQL syntax
"""

# Table schemas (can be auto-generated with auto_context_generator.py)
CONTEXT_TABLES = """
### TABLE_SCHEMAS

Table "mytable"
  id | integer | not null
  name | varchar(255) | not null
  created_at | timestamp | not null
"""

# Foreign key relationships
CONTEXT_JOINS = """
### TABLE_JOINS

digraph FKs {
  // Define your relationships here
}
"""

# Database metadata
DATABASE_TYPE = "postgresql"  # or your database type
SCHEMA_NAME = "public"

def get_context():
    """Get MyNewDB database context."""
    from db_context import get_context as get_db_context
    return get_db_context(CONTEXT_RULES, CONTEXT_TABLES, CONTEXT_JOINS, DATABASE_TYPE, SCHEMA_NAME)

def get_table_list():
    """Get list of MyNewDB tables."""
    return extract_table_names(CONTEXT_TABLES, SCHEMA_NAME)

if __name__ == "__main__":
    context = get_context()
    print(format_context_summary(context))
```

### 3. Auto-Generate Context (Optional)

Use the auto context generator to create the context automatically:

```python
from auto_context_generator import AutoContextGenerator

db_config = {
    'type': 'postgresql',
    'host': 'localhost',
    'port': 5432,
    'database': 'mynewdb',
    'user': 'postgres',
    'password': 'password'
}

generator = AutoContextGenerator(db_config, debug=True)
generator.generate_context_file(
    db_label='mynewdb',
    table_names=['table1', 'table2', 'table3'],
    schema='public',
    output_dir='dbs/mynewdb'
)
```

## Programmatic Usage

### Using the Agent Factory

```python
from db_agent_factory import create_agent, get_available_databases

# List available databases
databases = get_available_databases()
print(f"Available: {list(databases.keys())}")

# Create agent for any database
agent = create_agent('cb', model='gpt-4o-mini', debug=True)

# Test connection
result = agent.test_connection()
print(f"Connection: {result['success']}")

# Run query
result = agent.query("Count organizations", execute=True)
print(f"SQL: {result['sql_generation']['sql_query']}")
print(f"Results: {result['sql_execution']['results']}")
```

### Direct Component Usage

```python
from sql_gen import SQLGenerator
from sql_exec import SQLExecutor
from dbs.cb.context import get_context
from dbs.cb.connect import get_cb_db_config

# Create components directly
context = get_context()
db_config = get_cb_db_config()

sql_gen = SQLGenerator(context=context, model='gpt-4o-mini')
sql_exec = SQLExecutor(db_config)

# Generate and execute
sql_result = sql_gen.generate_sql("Count organizations")
exec_result = sql_exec.execute_sql(sql_result['sql_query'])
```

## Benefits of General Purpose Design

✅ **No Hardcoded Databases**: Automatically discovers any database in `dbs/`  
✅ **Easy Extension**: Add new databases by creating directory structure  
✅ **Consistent Interface**: Same CLI and API for all databases  
✅ **Auto-Discovery**: No need to modify code when adding databases  
✅ **Flexible Configuration**: Each database has its own connection config  
✅ **Modular Design**: Reusable components across databases  

## Migration from Hardcoded System

Old hardcoded approach:
```python
from agent_cb import CrunchBaseSQLAgent  # Hardcoded!
agent = CrunchBaseSQLAgent()
```

New general purpose approach:
```python
from db_agent_factory import create_agent  # General purpose!
agent = create_agent('cb')  # Any discovered database
```

The system now works with any database that follows the `dbs/{name}/` structure, making it truly general purpose and extensible.
