#!/usr/bin/env python3
"""
Generate AgBase database context using the auto context generator.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from auto_context_generator import AutoContextGenerator

def main():
    """Generate AgBase database context."""
    
    # Database configuration (same as CB since it's the same database)
    db_config = {
        'type': 'postgresql',
        'host': 'localhost',
        'port': 15432,
        'database': 'agbase1',
        'user': 'postgres',
        'password': 'password'
    }
    
    # Extract table names from agbasedb.txt
    agbase_tables = [
        'agbase_agtechcategory',
        'agbase_chat',
        'agbase_frview_incl_cache',
        'agbase_frview_pre_tbl',
        'agbase_fundinground',
        'agbase_fundingroundcode',
        'agbase_fundinground_investors',
        'agbase_label',
        'agbase_org',
        'agbase_org_affil_org_all',
        'agbase_org_alltags',
        'agbase_orgset',
        'agbase_orgset_orgs',
        'agbase_org_tags',
        'agbase_tagtree',
        'agbase_tagtree_labels',
        'agbase_tagtree_virtual_children'
    ]
    
    agsearch_tables = [
        'agsearch_ascontext',
        'agsearch_asdoc',
        'agsearch_asquery',
        'agsearch_asrating',
        'agsearch_company_status'
    ]
    
    # Combine all tables
    all_tables = agbase_tables + agsearch_tables
    
    print(f"Generating context for {len(all_tables)} AgBase tables...")
    
    # Generate context
    generator = AutoContextGenerator(db_config, debug=True)
    
    try:
        # Generate context file
        output_file = generator.generate_context_file(
            db_label='agbasedb',
            table_names=all_tables,
            schema='public',  # AgBase tables are in public schema
            output_dir='dbs/agbasedb'
        )
        
        # Rename to context.py to match the structure
        final_file = 'dbs/agbasedb/context.py'
        os.rename(output_file, final_file)
        
        print(f"✅ Successfully generated: {final_file}")
        
        # Also create a minimal connect.py file
        connect_content = '''#!/usr/bin/env python3
"""
AgBase Database Connection Configuration

Minimal configuration data for the AgBase database tables.
All boilerplate code is in db_connect.py.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_connect import get_db_config, test_connection_config, print_connection_info

# Default connection configuration for AgBase database (same as CB since same DB)
DEFAULT_AGBASEDB_CONFIG = {
    'type': 'postgresql',
    'host': 'localhost',
    'port': 15432,
    'database': 'agbase1',
    'user': 'postgres',
    'password': 'password',
    'schema': 'public'
}

# Environment variable mappings for configuration override
ENV_VAR_MAPPING = {
    'host': 'AGBASEDB_DB_HOST',
    'port': 'AGBASEDB_DB_PORT', 
    'database': 'AGBASEDB_DB_NAME',
    'user': 'AGBASEDB_DB_USER',
    'password': 'AGBASEDB_DB_PASSWORD',
    'schema': 'AGBASEDB_DB_SCHEMA'
}


def get_agbasedb_config(override_config=None):
    """Get AgBase database configuration."""
    return get_db_config(DEFAULT_AGBASEDB_CONFIG, ENV_VAR_MAPPING, override_config)


if __name__ == "__main__":
    config = get_agbasedb_config()
    print_connection_info(config, ENV_VAR_MAPPING)
    
    print(f"\\n=== Connection Test ===")
    test_result = test_connection_config(config)
    if test_result['success']:
        print("✅ Connection successful!")
        print(f"Database version: {test_result['database_version']}")
    else:
        print("❌ Connection failed!")
        print(f"Error: {test_result['message']}")
'''
        
        with open('dbs/agbasedb/connect.py', 'w') as f:
            f.write(connect_content)
        
        print("✅ Successfully generated: dbs/agbasedb/connect.py")
        
        # Test the generated files
        print("\\n=== Testing Generated Files ===")
        try:
            from dbs.agbasedb.context import get_context, get_table_list
            from dbs.agbasedb.connect import get_agbasedb_config
            
            context = get_context()
            tables = get_table_list()
            config = get_agbasedb_config()
            
            print(f"✅ Context loaded: {context['database_type']}")
            print(f"✅ Tables loaded: {len(tables)} tables")
            print(f"✅ Config loaded: {config['database']}")
            
        except Exception as e:
            print(f"❌ Error testing generated files: {e}")
        
    except Exception as e:
        print(f"❌ Error generating context: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
