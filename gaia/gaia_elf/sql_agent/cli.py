#!/usr/bin/env python3
"""
Command Line Interface for SQL Agent

This script provides a command-line interface to run SQL queries using
different database agents (CrunchBase, AgBase, etc.).

Example cmd lines:

python cli.py "Count rounds for facebook.com"

"""

import sys
import os
import argparse
import json

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from db_agent_factory import get_available_databases, create_agent


# Functions are now imported from db_agent_factory


def run_query(query, database='cb', execute=True, debug=False, model=None):
    """
    Run a single query using the SQL agent.

    Args:
        query: Natural language query string
        database: Database to query ('cb', 'agbase')
        execute: Whether to execute the generated SQL
        debug: Enable debug output
        model: LLM model to use

    Returns:
        Dictionary with results
    """
    try:
        # Initialize agent for the specified database
        agent = create_agent(database, model, debug)

        # Test connection first
        conn_test = agent.test_connection()
        if not conn_test['success']:
            return {
                'error': f"Database connection failed: {conn_test['message']}",
                'success': False
            }

        # Run the query
        result = agent.query(query, execute=execute)
        result['success'] = True
        result['database'] = database
        return result

    except Exception as e:
        return {
            'error': f"Error running query: {str(e)}",
            'success': False
        }


def format_output(result, format_type='human'):
    """
    Format the output for display.
    
    Args:
        result: Result dictionary from run_query
        format_type: Output format ('human', 'json', 'sql')
    """
    if not result.get('success'):
        print(f"❌ Error: {result.get('error', 'Unknown error')}")
        return
    
    sql_gen = result['sql_generation']
    sql_exec = result.get('sql_execution')
    
    if format_type == 'json':
        print(json.dumps(result, indent=2))
        return
    
    if format_type == 'sql':
        if sql_gen.get('sql_query'):
            print(sql_gen['sql_query'])
        else:
            print("-- No SQL generated")
        return
    
    # Human-readable format
    database = result.get('database', 'unknown')
    db_name = get_available_databases().get(database, {}).get('name', database.upper())
    print(f"Database: {db_name}")
    print(f"Query: {result['user_query']}")
    print("=" * 60)
    
    if sql_gen.get('sql_query'):
        print("Generated SQL:")
        print(sql_gen['sql_query'])
        print()
        
        if sql_gen.get('explanation'):
            print(f"Explanation: {sql_gen['explanation']}")
            print()
        
        if sql_gen.get('tables_used'):
            print(f"Tables used: {', '.join(sql_gen['tables_used'])}")
            print()
        
        if sql_exec:
            if sql_exec['success']:
                print("✅ Execution successful")
                if sql_exec.get('results'):
                    print(f"Results ({len(sql_exec['results'])} rows):")
                    for i, row in enumerate(sql_exec['results'][:10]):  # Show first 10 rows
                        print(f"  {i+1}: {row}")
                    if len(sql_exec['results']) > 10:
                        print(f"  ... and {len(sql_exec['results']) - 10} more rows")
                else:
                    print("No results returned")
            else:
                print(f"❌ Execution failed: {sql_exec['error']}")
    else:
        print("❌ Failed to generate SQL")
        if sql_gen.get('error'):
            print(f"Error: {sql_gen['error']}")


def interactive_mode(database='cb', debug=False, model=None):
    """
    Run the agent in interactive mode.

    Args:
        database: Database to use
        debug: Enable debug output
        model: LLM model to use
    """
    databases = get_available_databases()
    db_name = databases.get(database, {}).get('name', database.upper())

    print(f"=== Interactive SQL Agent - {db_name} Database ===")
    print("Type 'help' for commands, 'quit' to exit")
    print()

    try:
        agent = create_agent(database, model, debug)

        # Test connection
        conn_test = agent.test_connection()
        if not conn_test['success']:
            print(f"❌ Database connection failed: {conn_test['message']}")
            return

        print("✅ Database connection successful")
        print()

        while True:
            try:
                query = input(f"{database.upper()} Query> ").strip()

                if not query:
                    continue

                if query.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break

                if query.lower() in ['help', 'h']:
                    print("Commands:")
                    print("  help, h     - Show this help")
                    print("  quit, q     - Exit interactive mode")
                    print("  Any other text will be treated as a natural language query")
                    print()
                    continue

                # Run the query
                result = agent.query(query, execute=True)
                format_output(result, 'human')
                print()

            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except EOFError:
                print("\nGoodbye!")
                break

    except Exception as e:
        print(f"❌ Error in interactive mode: {e}")


def main():
    """Main command line interface."""
    # Discover available databases
    databases = get_available_databases()
    db_choices = list(databases.keys())

    # Create database descriptions for help
    db_descriptions = []
    for db_id, db_info in databases.items():
        db_descriptions.append(f"  {db_id}: {db_info['description']}")

    parser = argparse.ArgumentParser(
        description="Multi-Database SQL Agent - Natural language to SQL queries",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
Available Databases:
{chr(10).join(db_descriptions)}

Examples:
  # Run a single query (default database)
  python cli.py "Count organizations"

  # Query specific database
  python cli.py "Count organizations" --database cb
  python cli.py "Find AgTech companies" --database agbase

  # Generate SQL only (don't execute)
  python cli.py "Find investors in California" --no-execute

  # Output as JSON
  python cli.py "Show companies founded after 2020" --format json

  # Output SQL only
  python cli.py "List all funding rounds" --format sql

  # Interactive mode
  python cli.py --interactive --database cb

  # Use different model
  python cli.py "Count total organizations" --model gpt-4o-mini

  # Enable debug output
  python cli.py "Find companies" --debug

  # List available databases
  python cli.py --list-databases
        """
    )
    
    parser.add_argument(
        'query',
        nargs='?',
        help='Natural language query to convert to SQL'
    )

    parser.add_argument(
        '--database', '--db',
        choices=db_choices,
        default=db_choices[0] if db_choices else None,
        help=f'Database to query (default: {db_choices[0] if db_choices else "none"})'
    )

    parser.add_argument(
        '--list-databases',
        action='store_true',
        help='List available databases and exit'
    )

    parser.add_argument(
        '--no-execute',
        action='store_true',
        help='Generate SQL but do not execute it'
    )

    parser.add_argument(
        '--format',
        choices=['human', 'json', 'sql'],
        default='human',
        help='Output format (default: human)'
    )

    parser.add_argument(
        '--model',
        help='LLM model to use (default: anthropic/claude-sonnet-4-20250514)'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug output'
    )
    
    parser.add_argument(
        '--interactive',
        action='store_true',
        help='Run in interactive mode'
    )
    
    args = parser.parse_args()

    # List databases mode
    if args.list_databases:
        print("Available Databases:")
        print("=" * 50)
        for db_id, db_info in databases.items():
            print(f"{db_id:12} - {db_info['description']}")
            if 'database_type' in db_info:
                print(f"{'':12}   Type: {db_info['database_type']}")
        return

    # Check if any databases are available
    if not databases:
        print("❌ No databases found in dbs/ directory")
        print("Make sure you have database modules in dbs/{database_name}/ with context.py and connect.py")
        return

    # Interactive mode
    if args.interactive:
        interactive_mode(database=args.database, debug=args.debug, model=args.model)
        return

    # Single query mode
    if not args.query:
        parser.error("Query is required unless using --interactive or --list-databases mode")

    # Run the query
    result = run_query(
        query=args.query,
        database=args.database,
        execute=not args.no_execute,
        debug=args.debug,
        model=args.model
    )

    # Format and display output
    format_output(result, args.format)


if __name__ == "__main__":
    main()
