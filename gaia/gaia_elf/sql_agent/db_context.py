#!/usr/bin/env python3
"""
General Database Context Utilities

This module contains generic database context utilities that can be used
with any database schema context. Database-specific contexts should only contain
the minimal schema data.
"""

from typing import Dict, List, Any


def get_context(rules: str, tables: str, joins: str, database_type: str, schema_name: str = None) -> Dict[str, Any]:
    """
    Get the complete context for a database.
    
    Args:
        rules: Database-specific rules and guidelines
        tables: Table schema information
        joins: Table relationship information
        database_type: Type of database (PostgreSQL, MySQL, etc.)
        schema_name: Optional schema name
        
    Returns:
        Dictionary containing all context information
    """
    context = {
        'rules': rules,
        'tables': tables,
        'joins': joins,
        'database_type': database_type
    }
    
    if schema_name:
        context['schema_name'] = schema_name
    
    return context


def extract_table_names(tables_schema: str, schema_prefix: str = None) -> List[str]:
    """
    Extract table names from a schema string.
    
    Args:
        tables_schema: String containing table schema information
        schema_prefix: Optional schema prefix to add to table names
        
    Returns:
        List of table names
    """
    table_names = []
    
    # Look for table definitions in the schema string
    lines = tables_schema.split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('Table "') and line.endswith('"'):
            # Extract table name from: Table "schema.table_name"
            table_def = line[7:-1]  # Remove 'Table "' and '"'
            if schema_prefix and not table_def.startswith(schema_prefix):
                table_def = f"{schema_prefix}.{table_def.split('.')[-1]}"
            table_names.append(table_def)
    
    return table_names


def parse_join_relationships(joins_info: str) -> List[Dict[str, str]]:
    """
    Parse join relationship information from a joins string.
    
    Args:
        joins_info: String containing join relationship information
        
    Returns:
        List of dictionaries describing relationships
    """
    relationships = []
    
    lines = joins_info.split('\n')
    for line in lines:
        line = line.strip()
        if '->' in line and 'label=' in line:
            # Parse digraph relationship lines
            parts = line.split('->')
            if len(parts) == 2:
                from_table = parts[0].strip().strip('"')
                to_part = parts[1].strip()
                
                # Extract to_table and relationship from label
                if 'label=' in to_part:
                    to_table = to_part.split('[')[0].strip().strip('"')
                    label_part = to_part.split('label=')[1].strip().strip('"]')
                    
                    relationships.append({
                        'from_table': from_table,
                        'to_table': to_table,
                        'relationship': label_part
                    })
    
    return relationships


def get_common_query_patterns() -> Dict[str, str]:
    """
    Get common SQL query patterns that work across databases.
    
    Returns:
        Dictionary of query pattern descriptions
    """
    return {
        'count_records': "Count total records in a table",
        'filter_by_field': "Filter records by a specific field value",
        'join_tables': "Join multiple tables using foreign keys",
        'aggregate_data': "Aggregate data using GROUP BY and functions",
        'sort_results': "Sort results by one or more columns",
        'limit_results': "Limit the number of results returned"
    }


def validate_context(context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate that a context dictionary has all required fields.
    
    Args:
        context: Context dictionary to validate
        
    Returns:
        Dictionary with validation results
    """
    required_fields = ['rules', 'tables', 'joins', 'database_type']
    missing_fields = []
    
    for field in required_fields:
        if field not in context or not context[field]:
            missing_fields.append(field)
    
    return {
        'valid': len(missing_fields) == 0,
        'missing_fields': missing_fields,
        'has_schema': 'schema_name' in context and context['schema_name']
    }


def format_context_summary(context: Dict[str, Any]) -> str:
    """
    Format a context dictionary into a readable summary.
    
    Args:
        context: Context dictionary
        
    Returns:
        Formatted summary string
    """
    summary_lines = [
        "=== Database Context Summary ===",
        f"Database Type: {context.get('database_type', 'Unknown')}",
        f"Schema: {context.get('schema_name', 'Default')}"
    ]
    
    # Count tables
    if context.get('tables'):
        table_count = context['tables'].count('Table "')
        summary_lines.append(f"Tables: {table_count} defined")
    
    # Count rules
    if context.get('rules'):
        rule_lines = [line for line in context['rules'].split('\n') if line.strip().startswith('-')]
        summary_lines.append(f"Rules: {len(rule_lines)} defined")
    
    # Count relationships
    if context.get('joins'):
        relationships = parse_join_relationships(context['joins'])
        summary_lines.append(f"Relationships: {len(relationships)} defined")
    
    return '\n'.join(summary_lines)


def merge_contexts(base_context: Dict[str, Any], override_context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two context dictionaries, with override taking precedence.
    
    Args:
        base_context: Base context dictionary
        override_context: Override context dictionary
        
    Returns:
        Merged context dictionary
    """
    merged = base_context.copy()
    
    for key, value in override_context.items():
        if key in ['rules', 'tables', 'joins'] and value:
            # For text fields, append if both exist
            if key in merged and merged[key]:
                merged[key] = f"{merged[key]}\n\n{value}"
            else:
                merged[key] = value
        else:
            # For other fields, override completely
            merged[key] = value
    
    return merged


if __name__ == "__main__":
    # Example usage
    example_rules = "### RULES\n- Use proper indexing\n- Avoid SELECT *"
    example_tables = 'Table "users"\nTable "orders"'
    example_joins = 'orders -> users [label="user_id → id"]'
    
    context = get_context(
        rules=example_rules,
        tables=example_tables,
        joins=example_joins,
        database_type="PostgreSQL",
        schema_name="public"
    )
    
    print("=== General Database Context Utilities ===")
    print(format_context_summary(context))
    
    validation = validate_context(context)
    print(f"\nValidation: {'✅ Valid' if validation['valid'] else '❌ Invalid'}")
    
    if not validation['valid']:
        print(f"Missing fields: {validation['missing_fields']}")
    
    tables = extract_table_names(example_tables, "public")
    print(f"\nExtracted tables: {tables}")
    
    relationships = parse_join_relationships(example_joins)
    print(f"Parsed relationships: {relationships}")
