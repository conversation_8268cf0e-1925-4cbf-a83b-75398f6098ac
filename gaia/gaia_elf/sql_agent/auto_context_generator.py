#!/usr/bin/env python3
"""
Automatic Database Context Generator

This module automatically generates database context files by inspecting
database schemas and foreign key relationships.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

import psycopg2
import psycopg2.extras
from typing import Dict, List, Any, Optional
import re


class AutoContextGenerator:
    """
    Automatically generate database context by inspecting database schema.
    """
    
    def __init__(self, db_config: Dict[str, Any], debug: bool = False):
        """
        Initialize the context generator.
        
        Args:
            db_config: Database configuration dictionary
            debug: Enable debug output
        """
        self.db_config = db_config
        self.debug = debug
    
    def get_connection(self):
        """Get database connection."""
        return psycopg2.connect(
            host=self.db_config['host'],
            port=self.db_config['port'],
            database=self.db_config['database'],
            user=self.db_config['user'],
            password=self.db_config.get('password')
        )
    
    def get_table_schemas(self, table_names: List[str], schema: str = 'public') -> Dict[str, Any]:
        """
        Get detailed schema information for specified tables.
        
        Args:
            table_names: List of table names to inspect
            schema: Database schema name
            
        Returns:
            Dictionary with table schema information
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            schemas = {}
            
            for table_name in table_names:
                # Get column information
                cursor.execute("""
                    SELECT 
                        column_name,
                        data_type,
                        is_nullable,
                        column_default,
                        character_maximum_length,
                        numeric_precision,
                        numeric_scale
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s
                    ORDER BY ordinal_position
                """, (schema, table_name))
                
                columns = cursor.fetchall()
                
                # Get primary key information
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.key_column_usage kcu
                    JOIN information_schema.table_constraints tc 
                        ON kcu.constraint_name = tc.constraint_name
                    WHERE tc.table_schema = %s 
                        AND tc.table_name = %s 
                        AND tc.constraint_type = 'PRIMARY KEY'
                    ORDER BY kcu.ordinal_position
                """, (schema, table_name))
                
                primary_keys = [row['column_name'] for row in cursor.fetchall()]
                
                # Get indexes
                cursor.execute("""
                    SELECT 
                        indexname,
                        indexdef
                    FROM pg_indexes 
                    WHERE schemaname = %s AND tablename = %s
                """, (schema, table_name))
                
                indexes = cursor.fetchall()
                
                schemas[table_name] = {
                    'columns': [dict(col) for col in columns],
                    'primary_keys': primary_keys,
                    'indexes': [dict(idx) for idx in indexes]
                }
                
                if self.debug:
                    print(f"Analyzed table {table_name}: {len(columns)} columns, {len(primary_keys)} PKs")
            
            return schemas
    
    def get_foreign_keys(self, table_names: List[str], schema: str = 'public') -> List[Dict[str, str]]:
        """
        Get foreign key relationships for specified tables.
        
        Args:
            table_names: List of table names to inspect
            schema: Database schema name
            
        Returns:
            List of foreign key relationship dictionaries
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Get foreign key constraints
            cursor.execute("""
                SELECT 
                    tc.table_name as from_table,
                    kcu.column_name as from_column,
                    ccu.table_name as to_table,
                    ccu.column_name as to_column,
                    tc.constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage ccu 
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = %s
                    AND tc.table_name = ANY(%s)
                ORDER BY tc.table_name, kcu.ordinal_position
            """, (schema, table_names))
            
            foreign_keys = [dict(fk) for fk in cursor.fetchall()]
            
            if self.debug:
                print(f"Found {len(foreign_keys)} foreign key relationships")
            
            return foreign_keys
    
    def generate_rules(self, db_type: str = 'PostgreSQL') -> str:
        """
        Generate database-specific rules.
        
        Args:
            db_type: Type of database
            
        Returns:
            Rules string
        """
        if db_type.lower() == 'postgresql':
            return """### RULES
- Use PostgreSQL syntax and functions
- Use ILIKE for case-insensitive pattern matching
- Use proper JOINs based on foreign key relationships
- Consider using indexes for performance
- Use appropriate data type casting when needed
- Return valid PostgreSQL syntax"""
        else:
            return """### RULES
- Use appropriate database syntax
- Use proper JOINs based on foreign key relationships
- Consider performance implications
- Return valid SQL syntax"""
    
    def generate_tables_context(self, schemas: Dict[str, Any], schema_name: str = 'public') -> str:
        """
        Generate table schema context string.
        
        Args:
            schemas: Table schema information
            schema_name: Database schema name
            
        Returns:
            Formatted table context string
        """
        context_lines = ["### TABLE_SCHEMAS", ""]
        
        for table_name, table_info in schemas.items():
            full_table_name = f"{schema_name}.{table_name}" if schema_name != 'public' else table_name
            context_lines.append(f'Table "{full_table_name}"')
            
            # Add column information
            for col in table_info['columns']:
                col_line = f"  {col['column_name']} | {col['data_type']}"
                if col['character_maximum_length']:
                    col_line += f"({col['character_maximum_length']})"
                elif col['numeric_precision']:
                    col_line += f"({col['numeric_precision']}"
                    if col['numeric_scale']:
                        col_line += f",{col['numeric_scale']}"
                    col_line += ")"
                
                col_line += f" | {'NOT NULL' if col['is_nullable'] == 'NO' else 'NULL'}"
                
                if col['column_default']:
                    col_line += f" | DEFAULT {col['column_default']}"
                
                context_lines.append(col_line)
            
            # Add primary key info
            if table_info['primary_keys']:
                context_lines.append(f"  PRIMARY KEY: {', '.join(table_info['primary_keys'])}")
            
            context_lines.append("")
        
        return "\n".join(context_lines)
    
    def generate_joins_context(self, foreign_keys: List[Dict[str, str]], schema_name: str = 'public') -> str:
        """
        Generate joins context string.
        
        Args:
            foreign_keys: Foreign key relationship information
            schema_name: Database schema name
            
        Returns:
            Formatted joins context string
        """
        context_lines = ["### TABLE_JOINS", ""]
        
        if foreign_keys:
            context_lines.append("digraph FKs {")
            
            for fk in foreign_keys:
                from_table = f"{schema_name}.{fk['from_table']}" if schema_name != 'public' else fk['from_table']
                to_table = f"{schema_name}.{fk['to_table']}" if schema_name != 'public' else fk['to_table']
                
                context_lines.append(f'  "{from_table}" -> "{to_table}" [')
                context_lines.append(f'    label="{fk["from_column"]} → {fk["to_column"]}"')
                context_lines.append('  ];')
            
            context_lines.append("}")
            context_lines.append("")
            
            # Add relationship details
            context_lines.append("### RELATIONSHIP DETAILS:")
            for fk in foreign_keys:
                from_table = f"{schema_name}.{fk['from_table']}" if schema_name != 'public' else fk['from_table']
                to_table = f"{schema_name}.{fk['to_table']}" if schema_name != 'public' else fk['to_table']
                context_lines.append(f"- {from_table}.{fk['from_column']} → {to_table}.{fk['to_column']}")
        else:
            context_lines.append("No foreign key relationships found.")
        
        return "\n".join(context_lines)
    
    def generate_context_file(self, db_label: str, table_names: List[str], 
                            schema: str = 'public', output_dir: str = '.') -> str:
        """
        Generate a complete database context file.
        
        Args:
            db_label: Label for the database (e.g., 'cb', 'mysql_prod')
            table_names: List of table names to include
            schema: Database schema name
            output_dir: Output directory for the generated file
            
        Returns:
            Path to the generated context file
        """
        if self.debug:
            print(f"Generating context for {db_label} with {len(table_names)} tables...")
        
        # Get schema and foreign key information
        schemas = self.get_table_schemas(table_names, schema)
        foreign_keys = self.get_foreign_keys(table_names, schema)
        
        # Generate context components
        rules = self.generate_rules(self.db_config.get('type', 'PostgreSQL'))
        tables_context = self.generate_tables_context(schemas, schema)
        joins_context = self.generate_joins_context(foreign_keys, schema)
        
        # Generate the complete context file
        context_content = f'''#!/usr/bin/env python3
"""
{db_label.upper()} Database Context

Auto-generated database context for {db_label} database.
Generated from schema inspection of {len(table_names)} tables.
"""

from db_context import get_context, extract_table_names, format_context_summary

# Database-specific rules and guidelines
CONTEXT_RULES = """{rules}
"""

# Database table schemas
CONTEXT_TABLES = """{tables_context}
"""

# Table relationships and joins
CONTEXT_JOINS = """{joins_context}
"""

# Database metadata
DATABASE_TYPE = "{self.db_config.get('type', 'PostgreSQL')}"
SCHEMA_NAME = "{schema}"

# Common query patterns for {db_label}
COMMON_QUERIES = {{
    'record_count': "Count total records in tables",
    'table_info': "Get information about table structure",
    'relationship_queries': "Query data across related tables",
    'filtered_search': "Search and filter records"
}}


def get_context():
    """Get {db_label} database context."""
    from db_context import get_context as get_db_context
    return get_db_context(CONTEXT_RULES, CONTEXT_TABLES, CONTEXT_JOINS, DATABASE_TYPE, SCHEMA_NAME)


def get_table_list():
    """Get list of {db_label} tables."""
    return extract_table_names(CONTEXT_TABLES, SCHEMA_NAME)


def get_common_queries():
    """Get {db_label}-specific query examples."""
    return COMMON_QUERIES


if __name__ == "__main__":
    context = get_context()
    print(format_context_summary(context))
    print(f"\\nTables: {{get_table_list()}}")
    print(f"Common Queries: {{list(get_common_queries().keys())}}")
'''
        
        # Write to file
        output_file = os.path.join(output_dir, f'db_{db_label}_context.py')
        with open(output_file, 'w') as f:
            f.write(context_content)
        
        if self.debug:
            print(f"Generated context file: {output_file}")
        
        return output_file


def main():
    """Example usage of the auto context generator."""
    # Example configuration
    db_config = {
        'type': 'postgresql',
        'host': 'localhost',
        'port': 15432,
        'database': 'agbase1',
        'user': 'postgres',
        'password': 'password'
    }
    
    # Example table list
    table_names = ['cb16_organizations', 'cb16_funding_rounds', 'cb16_investments', 'cb16_investors']
    
    # Generate context
    generator = AutoContextGenerator(db_config, debug=True)
    
    try:
        output_file = generator.generate_context_file(
            db_label='cb_auto',
            table_names=table_names,
            schema='django'
        )
        print(f"\\n✅ Successfully generated: {output_file}")
        
    except Exception as e:
        print(f"❌ Error generating context: {e}")


if __name__ == "__main__":
    main()
