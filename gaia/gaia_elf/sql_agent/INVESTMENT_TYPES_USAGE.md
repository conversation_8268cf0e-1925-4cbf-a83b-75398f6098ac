# Investment Types Usage Guide

The CrunchBase database context now includes all 28 distinct investment types available in the `django.cb16_funding_rounds` table. This enables more precise and accurate SQL query generation.

## Available Investment Types

The following 28 investment types are available in the `investment_type` column:

### Series Rounds
- `series_a` - Series A funding
- `series_b` - Series B funding  
- `series_c` - Series C funding
- `series_d` - Series D funding
- `series_e` - Series E funding
- `series_f` - Series F funding
- `series_g` - Series G funding
- `series_h` - Series H funding
- `series_i` - Series I funding
- `series_j` - Series J funding
- `series_unknown` - Series round of unknown letter

### Early Stage
- `seed` - Seed funding
- `pre_seed` - Pre-seed funding
- `angel` - Angel investment

### Other Types
- `private_equity` - Private equity investment
- `corporate_round` - Corporate funding round
- `debt_financing` - Debt financing
- `undisclosed` - Undisclosed funding type
- `grant` - Grant funding
- `convertible_note` - Convertible note

### Crowdfunding
- `equity_crowdfunding` - Equity crowdfunding
- `product_crowdfunding` - Product/reward crowdfunding

### Post-IPO
- `post_ipo_secondary` - Post-IPO secondary offering
- `post_ipo_debt` - Post-IPO debt
- `post_ipo_equity` - Post-IPO equity

### Other
- `non_equity_assistance` - Non-equity assistance
- `secondary_market` - Secondary market transaction
- `initial_coin_offering` - ICO/cryptocurrency offering

## Usage Examples

### Query by Specific Investment Type
```sql
-- Find all Series A rounds
SELECT * FROM django.cb16_funding_rounds 
WHERE investment_type = 'series_a';

-- Find all seed rounds
SELECT * FROM django.cb16_funding_rounds 
WHERE investment_type = 'seed';
```

### Query by Multiple Investment Types
```sql
-- Find all early-stage rounds
SELECT * FROM django.cb16_funding_rounds 
WHERE investment_type IN ('seed', 'pre_seed', 'angel', 'series_a');

-- Find all series rounds
SELECT * FROM django.cb16_funding_rounds 
WHERE investment_type LIKE 'series_%';
```

### Natural Language Query Examples

With the investment types in context, the SQL agent can now handle queries like:

- **"Find all Series A funding rounds"**
- **"Count seed investments in 2020"**
- **"Show companies that raised angel funding"**
- **"List all private equity deals"**
- **"Find Series B rounds with more than $10M"**

### Sample Generated Queries

#### Query: "Find all Series A funding rounds"
```sql
SELECT fr.*, org.company_name
FROM django.cb16_funding_rounds fr
JOIN django.cb16_organizations org ON fr.company_uuid = org.uuid
WHERE fr.investment_type = 'series_a';
```

#### Query: "Count seed investments by year"
```sql
SELECT 
    EXTRACT(YEAR FROM announced_on::date) as year,
    COUNT(*) as seed_rounds
FROM django.cb16_funding_rounds
WHERE investment_type = 'seed'
    AND announced_on IS NOT NULL
GROUP BY EXTRACT(YEAR FROM announced_on::date)
ORDER BY year;
```

#### Query: "Show early-stage funding statistics"
```sql
SELECT 
    investment_type,
    COUNT(*) as round_count,
    AVG(raised_amount_usd::numeric) as avg_amount
FROM django.cb16_funding_rounds
WHERE investment_type IN ('seed', 'pre_seed', 'angel', 'series_a')
    AND raised_amount_usd IS NOT NULL
GROUP BY investment_type
ORDER BY round_count DESC;
```

## Context Integration

The investment types are automatically included in the database context when using:

```python
from dbs.cb.context import get_context, get_investment_types

# Get full context (includes investment types)
context = get_context()

# Get just the investment types list
investment_types = get_investment_types()
print(f"Available types: {len(investment_types)}")
```

## Benefits

✅ **More Accurate Queries**: SQL agent knows exact investment type values  
✅ **Better Filtering**: Can generate precise WHERE clauses  
✅ **Reduced Errors**: No guessing of investment type names  
✅ **Enhanced Context**: LLM has complete picture of available data  
✅ **Improved Suggestions**: Can suggest valid investment types in queries  

## Command Line Usage

```bash
# Query specific investment types
python cli.py "Find all Series A rounds in California"

# Compare investment types
python cli.py "Compare seed vs Series A funding amounts"

# Investment type statistics
python cli.py "Show distribution of investment types"
```

The SQL agent will now generate more accurate queries using the exact investment type values available in the database.
