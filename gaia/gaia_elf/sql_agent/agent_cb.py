#!/usr/bin/env python3
"""
CrunchBase SQL Query Agent

This module ties together the generic SQL generation and execution components
with the specific CrunchBase database context and connection configuration
to create a complete SQL query agent for the CB16 database.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from typing import Dict, Optional, Any
import json

# Import the generic components
from sql_gen import SQLGenerator
from sql_exec import SQLExecutor

# Import the CB-specific context and connection
from dbs.cb.context import get_context, get_table_list, get_common_queries
from dbs.cb.connect import get_cb_db_config


class CrunchBaseSQLAgent:
    """
    Complete SQL Agent for CrunchBase database that combines:
    - Generic SQL generation (sql_gen.py)
    - Generic SQL execution (sql_exec.py) 
    - CrunchBase database context (db_cb_context.py)
    - CrunchBase connection config (db_cb_connect.py)
    """
    
    def __init__(self, model: str = "anthropic/claude-sonnet-4-20250514", debug: bool = False,
                 db_config_override: Optional[Dict[str, Any]] = None):
        """
        Initialize the CrunchBase SQL Agent.
        
        Args:
            model: LLM model to use for SQL generation
            debug: Enable debug output
            db_config_override: Optional database configuration override
        """
        self.debug = debug
        
        # Initialize the generic SQL generator
        self.sql_generator = SQLGenerator(model=model, debug=debug)
        
        # Get CrunchBase database configuration
        self.db_config = get_cb_db_config(db_config_override)
        
        # Initialize the generic SQL executor
        self.sql_executor = SQLExecutor(self.db_config, debug=debug)
        
        # Get CrunchBase database context
        self.context = get_context()
        
        if self.debug:
            print(f"Initialized CrunchBase SQL Agent")
            print(f"Model: {model}")
            print(f"Database: {self.db_config['database']} on {self.db_config['host']}:{self.db_config['port']}")
    
    def generate_sql(self, user_query: str) -> Dict:
        """
        Generate SQL for a user query using CrunchBase context.
        
        Args:
            user_query: Natural language query
            
        Returns:
            Dictionary with SQL generation results
        """
        return self.sql_generator.generate_sql(
            context_rules=self.context['rules'],
            context_tables=self.context['tables'],
            context_joins=self.context['joins'],
            user_query=user_query,
            database_type=self.context['database_type']
        )
    
    def execute_sql(self, sql_query: str, fetch_results: bool = True) -> Dict:
        """
        Execute a SQL query against the CrunchBase database.
        
        Args:
            sql_query: SQL query to execute
            fetch_results: Whether to fetch and return results
            
        Returns:
            Dictionary with execution results
        """
        return self.sql_executor.execute_sql(sql_query, fetch_results)
    
    def query(self, user_query: str, execute: bool = True) -> Dict:
        """
        Complete query processing: generate SQL and optionally execute it.
        
        Args:
            user_query: Natural language query
            execute: Whether to execute the generated SQL
            
        Returns:
            Dictionary with generation and execution results
        """
        # Generate SQL
        generation_result = self.generate_sql(user_query)
        
        # Prepare combined result
        result = {
            'user_query': user_query,
            'sql_generation': generation_result,
            'sql_execution': None
        }
        
        # Execute SQL if requested and generation was successful
        if execute and generation_result.get('sql_query') and not generation_result.get('error'):
            sql_query = generation_result['sql_query']
            execution_result = self.execute_sql(sql_query)
            result['sql_execution'] = execution_result
            
            if self.debug:
                if execution_result['success']:
                    print(f"=== SQL EXECUTION SUCCESSFUL ===")
                    print(f"Rows returned: {execution_result.get('result_count', execution_result.get('rowcount', 0))}")
                    if execution_result.get('results'):
                        print(f"Sample results:")
                        for i, row in enumerate(execution_result['results'][:3]):
                            print(f"  Row {i+1}: {row}")
                        if len(execution_result['results']) > 3:
                            print(f"  ... and {len(execution_result['results']) - 3} more rows")
                else:
                    print(f"=== SQL EXECUTION FAILED ===")
                    print(f"Error: {execution_result['error']}")
        
        return result
    
    def test_connection(self) -> Dict:
        """
        Test the database connection.
        
        Returns:
            Dictionary with connection test results
        """
        return self.sql_executor.test_connection()
    
    def get_table_info(self) -> Dict:
        """
        Get information about CrunchBase tables.
        
        Returns:
            Dictionary with table information
        """
        return self.sql_executor.get_table_info(schema=self.context['schema_name'])
    
    def get_available_tables(self) -> list:
        """
        Get list of available tables.
        
        Returns:
            List of table names
        """
        return get_table_list()
    
    def get_query_examples(self) -> Dict:
        """
        Get examples of common queries for this database.
        
        Returns:
            Dictionary of query examples
        """
        return get_common_queries()
    
    def interactive_mode(self):
        """
        Run the agent in interactive mode for testing queries.
        """
        print("=== CrunchBase SQL Agent - Interactive Mode ===")
        print("Enter natural language queries to generate and execute SQL.")
        print("Type 'quit', 'exit', or 'q' to exit.")
        print("Type 'help' for examples.\n")
        
        while True:
            try:
                user_input = input("CB Query> ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    examples = self.get_query_examples()
                    print("\nExample queries:")
                    for key, description in examples.items():
                        print(f"  - {description}")
                    print()
                    continue
                
                if not user_input:
                    continue
                
                print(f"\nProcessing: {user_input}")
                result = self.query(user_input, execute=True)
                
                print("\n" + "="*60)
                
                # Show SQL generation results
                sql_gen = result['sql_generation']
                if sql_gen.get('sql_query'):
                    print("Generated SQL:")
                    print(sql_gen['sql_query'])
                    print(f"\nExplanation: {sql_gen.get('explanation', 'N/A')}")
                    
                    # Show execution results
                    sql_exec = result.get('sql_execution')
                    if sql_exec:
                        if sql_exec['success']:
                            print(f"\n✅ Execution successful")
                            if sql_exec.get('results'):
                                print(f"Results ({len(sql_exec['results'])} rows):")
                                for i, row in enumerate(sql_exec['results'][:5]):
                                    print(f"  {i+1}: {row}")
                                if len(sql_exec['results']) > 5:
                                    print(f"  ... and {len(sql_exec['results']) - 5} more rows")
                            else:
                                print("No results returned")
                        else:
                            print(f"❌ Execution failed: {sql_exec['error']}")
                else:
                    print("❌ Failed to generate SQL")
                    if sql_gen.get('error'):
                        print(f"Error: {sql_gen['error']}")
                
                print("="*60 + "\n")
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {e}\n")


def main():
    """
    Example usage of the CrunchBase SQL Agent
    """
    # Initialize the agent
    agent = CrunchBaseSQLAgent(
        model="anthropic/claude-sonnet-4-20250514",
        debug=True
    )
    
    # Test connection
    print("=== Testing Connection ===")
    conn_test = agent.test_connection()
    if not conn_test['success']:
        print(f"❌ Connection failed: {conn_test['message']}")
        return
    print("✅ Connection successful")
    
    # Example query
    print("\n=== Example Query ===")
    result = agent.query("Count rounds for facebook.com", execute=True)
    
    print(f"User Query: {result['user_query']}")
    print(f"Generated SQL: {result['sql_generation'].get('sql_query')}")
    
    if result['sql_execution'] and result['sql_execution']['success']:
        print(f"Results: {result['sql_execution']['results']}")
    
    # Show available functionality
    print(f"\n=== Available Tables ===")
    tables = agent.get_available_tables()
    for table in tables:
        print(f"  - {table}")
    
    print(f"\n=== Query Examples ===")
    examples = agent.get_query_examples()
    for description in examples.values():
        print(f"  - {description}")


if __name__ == "__main__":
    main()
