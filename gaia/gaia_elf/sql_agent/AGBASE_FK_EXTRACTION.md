# AgBase Foreign Key Extraction

Successfully extracted and documented foreign key relationships for the AgBase database context.

## Extracted Foreign Key Relationships

### Core Relationships

1. **Funding Round to Company**
   - `agbase_fundinground.company_id → agbase_org.id`
   - Links funding rounds to the companies that received funding

2. **Funding Round to Investors (Many-to-Many)**
   - `agbase_fundinground_investors.fundinground_id → agbase_fundinground.id`
   - `agbase_fundinground_investors.investor_id → agbase_org.id`
   - Junction table connecting funding rounds to multiple investors

3. **Lead Investor Relationship**
   - `agbase_fundinground.lead_investor_id → agbase_org.id`
   - Links funding rounds to their lead investor

4. **Funding Round Codes**
   - `agbase_fundinground.fr_code_id → agbase_fundingroundcode.id`
   - Links funding rounds to their type/code definitions

5. **Organization Categories**
   - `agbase_org.category_id → agbase_agtechcategory.id`
   - Links organizations to their AgTech categories

6. **Organization Affiliations**
   - `agbase_org.affil_org_primary_id → agbase_org.id`
   - Self-referencing relationship for organization hierarchies

## Key Data Values Added

### Organization Roles (`org_role`)
- `'comp'` - Company/Organization
- `'inv'` - Investor
- `'pers'` - Person

### Organization Classes (`org_class`)
- `'comp'` - Company
- `'inv'` - Investor
- `'pers'` - Person
- `'acad'` - Academic institution

### Record Inclusion Status (`rec_inclusion`)
- `'yes'` - Included record
- `'no'` - Excluded record
- `'may'` - Maybe/uncertain

## Common Join Patterns

### 1. Organizations + Funding Rounds
```sql
FROM agbase_org org
JOIN agbase_fundinground fr ON fr.company_id = org.id
```

### 2. Funding Rounds + Investors
```sql
FROM agbase_fundinground fr
JOIN agbase_fundinground_investors fri ON fri.fundinground_id = fr.id
JOIN agbase_org investor ON fri.investor_id = investor.id
```

### 3. Complete Funding Data
```sql
FROM agbase_org company
JOIN agbase_fundinground fr ON fr.company_id = company.id
JOIN agbase_fundinground_investors fri ON fri.fundinground_id = fr.id
JOIN agbase_org investor ON fri.investor_id = investor.id
JOIN agbase_fundingroundcode frc ON fr.fr_code_id = frc.id
```

### 4. Organizations + Categories
```sql
FROM agbase_org org
JOIN agbase_agtechcategory cat ON org.category_id = cat.id
```

### 5. Organization Affiliations
```sql
FROM agbase_org org
JOIN agbase_org parent ON org.affil_org_primary_id = parent.id
```

## Helper Functions Added

- `get_key_relationships()` - Returns dictionary of key FK relationships
- `get_org_roles()` - Returns list of organization roles
- `get_org_classes()` - Returns list of organization classes
- `get_inclusion_statuses()` - Returns list of inclusion statuses

## Query Examples Now Supported

With the extracted foreign keys, the SQL agent can now handle queries like:

- **"Find all funding rounds for AgTech companies"**
- **"Show investors in Series A rounds"**
- **"List companies by category"**
- **"Find lead investors for recent funding rounds"**
- **"Show organization affiliations and subsidiaries"**
- **"Count investments by investor type"**

## Technical Implementation

### Before FK Extraction
```
CONTEXT_JOINS = """### TABLE_JOINS

No foreign key relationships found.
"""
```

### After FK Extraction
```
CONTEXT_JOINS = """### TABLE_JOINS

digraph FKs {
  "agbase_fundinground_investors" -> "agbase_fundinground" [
    label="fundinground_id → id"
  ];
  "agbase_fundinground_investors" -> "agbase_org" [
    label="investor_id → id"
  ];
  // ... 5 more relationships
}

### RELATIONSHIP DETAILS:
- agbase_org: Core organization/company data
- agbase_fundinground: Funding rounds linked to companies
- agbase_fundinground_investors: Many-to-many investor relationships
// ... detailed explanations

### COMMON JOIN PATTERNS:
// ... 5 common join patterns with SQL examples
"""
```

## Benefits Achieved

✅ **Proper JOIN Generation**: SQL agent can now generate correct JOINs  
✅ **Relationship Understanding**: Knows how tables connect to each other  
✅ **Query Accuracy**: Can navigate complex multi-table relationships  
✅ **Data Integrity**: Understands the data model structure  
✅ **Enhanced Context**: Complete picture of AgBase database relationships  

## Testing

Created comprehensive test suite (`test_agbase_fks.py`) that verifies:
- Foreign key relationships are properly included in context
- Helper functions work correctly
- Join patterns are documented
- Example queries are supported

All tests pass, confirming successful foreign key extraction and integration.

## Impact

The AgBase database context now provides the same level of relationship understanding as the CrunchBase context, enabling the SQL agent to generate sophisticated queries across the AgBase data model with proper table joins and relationship navigation.
