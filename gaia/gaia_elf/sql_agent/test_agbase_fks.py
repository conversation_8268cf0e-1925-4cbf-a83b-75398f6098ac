#!/usr/bin/env python3
"""
Test that foreign key relationships are properly extracted and included in AgBase context.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

def test_foreign_keys_in_context():
    """Test that foreign key relationships are included in the AgBase context."""
    print("=== Testing Foreign Key Relationships in AgBase Context ===\n")
    
    try:
        from dbs.agbasedb.context import get_context, get_key_relationships
        
        # Test the helper function
        relationships = get_key_relationships()
        print(f"✅ Key relationships function works: {len(relationships)} relationships")
        
        # Test that context includes the relationships
        context = get_context()
        joins_content = context['joins']
        
        # Check if foreign key relationships are in the context
        if 'TABLE_JOINS' in joins_content and 'digraph FKs' in joins_content:
            print("✅ Foreign key relationships section found in context")
        else:
            print("❌ Foreign key relationships section NOT found in context")
            return False
        
        # Check for specific relationships
        key_relationships = [
            'fundinground_id → id',
            'company_id → id',
            'investor_id → id',
            'lead_investor_id → id',
            'category_id → id'
        ]
        
        found_relationships = []
        for relationship in key_relationships:
            if relationship in joins_content:
                found_relationships.append(relationship)
        
        print(f"✅ Found {len(found_relationships)}/{len(key_relationships)} key relationships in context")
        print(f"Found relationships: {found_relationships}")
        
        # Check for join patterns
        if 'COMMON JOIN PATTERNS' in joins_content:
            print("✅ Common join patterns found in context")
        else:
            print("❌ Common join patterns not found in context")
        
        return len(found_relationships) >= 4  # At least 4 out of 5 should be found
        
    except Exception as e:
        print(f"❌ Error testing foreign keys: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_relationship_details():
    """Test the relationship helper functions."""
    print("\n=== Testing Relationship Helper Functions ===\n")
    
    try:
        from dbs.agbasedb.context import get_key_relationships, get_org_roles, get_org_classes
        
        # Test key relationships
        relationships = get_key_relationships()
        expected_keys = [
            'company_funding',
            'funding_investors', 
            'investor_details',
            'lead_investor',
            'funding_codes',
            'org_categories',
            'org_affiliations'
        ]
        
        found_keys = []
        for key in expected_keys:
            if key in relationships:
                found_keys.append(key)
        
        print(f"✅ Found {len(found_keys)}/{len(expected_keys)} expected relationship keys")
        print(f"Found keys: {found_keys}")
        
        # Test org roles
        org_roles = get_org_roles()
        expected_roles = ['comp', 'inv', 'pers']
        if set(org_roles) == set(expected_roles):
            print(f"✅ Organization roles correct: {org_roles}")
        else:
            print(f"❌ Organization roles incorrect: {org_roles}")
        
        # Test org classes
        org_classes = get_org_classes()
        expected_classes = ['comp', 'inv', 'pers', 'acad']
        if set(org_classes) == set(expected_classes):
            print(f"✅ Organization classes correct: {org_classes}")
        else:
            print(f"❌ Organization classes incorrect: {org_classes}")
        
        return len(found_keys) >= 6  # At least 6 out of 7 should be found
        
    except Exception as e:
        print(f"❌ Error testing relationship details: {e}")
        return False

def show_sample_joins():
    """Show sample join patterns from the context."""
    print("\n=== Sample Join Patterns ===\n")
    
    try:
        from dbs.agbasedb.context import get_context
        
        context = get_context()
        joins_content = context['joins']
        
        # Find and show join patterns section
        lines = joins_content.split('\n')
        in_patterns_section = False
        pattern_lines = []
        
        for line in lines:
            if 'COMMON JOIN PATTERNS' in line:
                in_patterns_section = True
                pattern_lines.append(line)
            elif in_patterns_section:
                pattern_lines.append(line)
                if len(pattern_lines) > 20:  # Limit output
                    pattern_lines.append("... (truncated)")
                    break
        
        if pattern_lines:
            print("Common Join Patterns:")
            for line in pattern_lines:
                print(line)
        else:
            print("❌ No join patterns section found")
        
        return len(pattern_lines) > 0
        
    except Exception as e:
        print(f"❌ Error showing join patterns: {e}")
        return False

def test_example_queries():
    """Test example queries that would use the foreign key relationships."""
    print("\n=== Example Query Scenarios ===\n")
    
    example_queries = [
        "Find all funding rounds for a specific company",
        "Show investors in a funding round",
        "List companies by category", 
        "Find lead investors for Series A rounds",
        "Show organization affiliations"
    ]
    
    print("These queries should now work with proper foreign key relationships:")
    for i, query in enumerate(example_queries, 1):
        print(f"{i}. \"{query}\"")
    
    print("\nWith the foreign key relationships, the SQL agent will understand:")
    print("• How to join funding rounds to companies")
    print("• How to connect investors to funding rounds")
    print("• How to link organizations to categories")
    print("• How to find lead investors")
    print("• How to navigate organization hierarchies")
    
    return True

def main():
    """Run all tests."""
    print("=== AgBase Foreign Key Extraction Test ===\n")
    
    tests = [
        ("Foreign Keys in Context", test_foreign_keys_in_context),
        ("Relationship Helper Functions", test_relationship_details),
        ("Sample Join Patterns", show_sample_joins),
        ("Example Query Scenarios", test_example_queries)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n=== Test Results ===")
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed!")
        print("✅ Foreign key relationships properly extracted from AgBase context")
        print("✅ SQL agent can now generate proper JOINs for AgBase queries")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
