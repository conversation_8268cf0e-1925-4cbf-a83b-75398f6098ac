# Modular SQL Agent System

A modular, extensible SQL agent system that separates generic SQL generation and execution from database-specific context and configuration.

## Architecture Overview

```
agent_cb.py (CrunchBase SQL Agent)
├── sql_gen.py (Generic SQL Generation)
│   └── Uses gaia_llm completion_json
├── sql_exec.py (Generic SQL Execution)  
│   └── Uses psycopg2 for PostgreSQL
├── db_cb_context.py (CrunchBase Schema)
│   ├── Table schemas and relationships
│   ├── Database-specific rules
│   └── Query examples
└── db_cb_connect.py (Connection Config)
    ├── Database connection parameters
    ├── Environment variable support
    └── Connection testing
```

## Components

### 1. Generic Components (Database-Agnostic)

#### `sql_gen.py` - SQL Generation
- **Purpose**: Generic SQL generation using LLM completion_json
- **Key Class**: `SQLGenerator`
- **Features**:
  - Takes context (rules, tables, joins) and user query
  - Generates SQL using gaia_llm completion_json
  - Returns structured response with SQL, explanation, and metadata
  - Database-type agnostic (PostgreSQL, MySQL, etc.)

#### `sql_exec.py` - SQL Execution  
- **Purpose**: Generic SQL execution against databases
- **Key Class**: `SQLExecutor`
- **Features**:
  - Database connection management with context managers
  - SQL query execution with error handling
  - Support for multiple database types (currently PostgreSQL)
  - Structured result formatting
  - Connection testing utilities

### 2. Database-Specific Components (CrunchBase)

#### `db_cb_context.py` - CrunchBase Database Context
- **Purpose**: CrunchBase-specific schema, rules, and metadata
- **Contents**:
  - `CONTEXT_RULES`: Database-specific query rules and guidelines
  - `CONTEXT_TABLES`: Complete table schemas for CB16 tables
  - `CONTEXT_JOINS`: Table relationships and common join patterns
  - Helper functions for context retrieval

#### `db_cb_connect.py` - CrunchBase Connection Configuration
- **Purpose**: CrunchBase database connection management
- **Features**:
  - Default connection configuration for agbase1 PostgreSQL
  - Environment variable override support
  - Connection string generation
  - psql command generation
  - Connection testing

### 3. Integrated Agent

#### `agent_cb.py` - CrunchBase SQL Agent
- **Purpose**: Complete SQL agent combining all components
- **Key Class**: `CrunchBaseSQLAgent`
- **Features**:
  - Natural language to SQL generation and execution
  - CrunchBase-specific context integration
  - Interactive query mode
  - Connection testing and table information

## Usage Examples

### Basic Usage

```python
from agent_cb import CrunchBaseSQLAgent

# Initialize the agent
agent = CrunchBaseSQLAgent(model="gpt-4o-mini", debug=False)

# Generate and execute SQL
result = agent.query("Count rounds for facebook.com", execute=True)

print("Generated SQL:", result['sql_generation']['sql_query'])
print("Results:", result['sql_execution']['results'])
```

### Component-Level Usage

```python
# Use SQL generator directly
from sql_gen import SQLGenerator
from db_cb_context import get_context

generator = SQLGenerator(model="gpt-4o-mini")
context = get_context()

sql_result = generator.generate_sql(
    context_rules=context['rules'],
    context_tables=context['tables'],
    context_joins=context['joins'],
    user_query="Find investors in California"
)

# Use SQL executor directly
from sql_exec import SQLExecutor
from db_cb_connect import get_cb_db_config

executor = SQLExecutor(get_cb_db_config())
exec_result = executor.execute_sql(sql_result['sql_query'])
```

### Interactive Mode

```python
agent = CrunchBaseSQLAgent()
agent.interactive_mode()  # Starts interactive query session
```

## Database Schema

The system works with the CrunchBase CB16 dataset containing:

- **`django.cb16_organizations`**: Company information (3,978,564 records)
- **`django.cb16_funding_rounds`**: Funding round details
- **`django.cb16_investments`**: Individual investment records  
- **`django.cb16_investors`**: Investor information

### Key Relationships
```sql
cb16_funding_rounds.company_uuid → cb16_organizations.uuid
cb16_investments.funding_round_uuid → cb16_funding_rounds.funding_round_uuid
cb16_investments.investor_uuid → cb16_investors.uuid
```

## Configuration

### Database Connection

Default configuration (can be overridden):
```python
{
    'type': 'postgresql',
    'host': 'localhost',
    'port': 15432,
    'database': 'agbase1',
    'user': 'postgres',
    'password': 'password',
    'schema': 'django'
}
```

### Environment Variables

Override connection settings using:
- `CB_DB_HOST`: Database host
- `CB_DB_PORT`: Database port
- `CB_DB_NAME`: Database name
- `CB_DB_USER`: Database user
- `CB_DB_PASSWORD`: Database password
- `CB_DB_SCHEMA`: Database schema

## Testing

Run the test suite:
```bash
python test_modular.py
```

Run the demonstration:
```bash
python demo_modular.py
```

## Benefits of Modular Design

✅ **Separation of Concerns**: Each component has a single responsibility
✅ **Reusability**: Generic components can be used with different databases
✅ **Testability**: Individual components can be tested in isolation
✅ **Extensibility**: Easy to add support for new databases
✅ **Maintainability**: Changes to one component don't affect others
✅ **Flexibility**: Can use components individually or together

## Adding New Databases

To add support for a new database:

1. Create `db_[name]_context.py` with schema and rules
2. Create `db_[name]_connect.py` with connection configuration
3. Create `agent_[name].py` that combines the generic components with your context
4. Update `sql_exec.py` if new database driver support is needed

## Dependencies

- `psycopg2-binary`: PostgreSQL database adapter
- `gaia.gaia_llm`: LLM completion functionality
- `psutil`: System utilities (required by gaia_llm)

## Files

- `sql_gen.py`: Generic SQL generation
- `sql_exec.py`: Generic SQL execution
- `db_cb_context.py`: CrunchBase database context
- `db_cb_connect.py`: CrunchBase connection configuration
- `agent_cb.py`: Complete CrunchBase SQL agent
- `test_modular.py`: Test suite for all components
- `demo_modular.py`: Demonstration of the modular system
- `README_MODULAR.md`: This documentation

## Example Queries

The system can handle queries like:
- "Count rounds for facebook.com" → 4 funding rounds
- "Find all investors from California"
- "Show companies that raised more than $1M"
- "List all Series A funding rounds"
- "Find investors who invested in AI companies"
