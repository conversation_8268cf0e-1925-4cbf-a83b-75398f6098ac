#!/usr/bin/env python3
"""
Generic SQL Generation Module

This module provides core SQL generation functionality using LLM completion_json.
It's database-agnostic and can be used with any schema context.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from gaia.gaia_llm import gaia_llm
from typing import Dict, Optional
import json


class SQLGenerator:
    """
    Generic SQL generator that uses gaia_llm completion_json to generate SQL queries
    based on provided context (tables, joins, rules).
    """
    
    def __init__(self, model: str = "anthropic/claude-sonnet-4-20250514", debug: bool = False):
        """
        Initialize the SQL Generator with a specific LLM model.
        
        Args:
            model: The LLM model to use for SQL generation
            debug: Enable debug output
        """
        self.llm = gaia_llm.Json_LlmClient_Cached(model=model, debug_out=debug)
        self.debug = debug
    
    def create_context_string(self, context_rules: str, context_tables: str, 
                            context_joins: str, database_type: str = "PostgreSQL") -> str:
        """
        Create a comprehensive context string from rules, tables and joins information.
        
        Args:
            context_rules: Database-specific rules and guidelines
            context_tables: Database table schema information
            context_joins: Table relationship information
            database_type: Type of database (PostgreSQL, MySQL, etc.)
            
        Returns:
            Formatted context string for the LLM
        """
        context = f"""
# DATABASE SCHEMA CONTEXT:

{context_rules.strip()}

{context_tables.strip()}

{context_joins.strip()}

IMPORTANT NOTES:
- Generate valid {database_type} syntax
- Use appropriate string matching techniques based on the rules provided
- Use proper JOINs based on the relationship diagram provided
- Return syntactically correct SQL
"""
        return context
        
    def create_prompt(self, context_string: str, user_query: str) -> str:
        """
        Create the complete prompt for SQL generation.
        
        Args:
            context_string: Database context information
            user_query: User's natural language query
            
        Returns:
            Complete prompt for the LLM
        """
        prompt = f"""{context_string}

USER REQUEST: {user_query}

Generate a SQL query to answer the user's request.

Return your response in the following JSON format:
{{
    "sql_query": "SELECT ... FROM ... WHERE ...",
    "explanation": "Brief explanation of what the query does",
    "tables_used": ["table1", "table2"],
    "assumptions": ["Any assumptions made about the data or query interpretation"]
}}

Ensure the SQL query is syntactically correct and uses the exact table and column names provided in the schema."""
        
        return prompt
        
    def generate_sql(self, context_rules: str, context_tables: str, 
                    context_joins: str, user_query: str, 
                    database_type: str = "PostgreSQL") -> Dict:
        """
        Generate SQL query based on context and user request.
        
        Args:
            context_rules: Database-specific rules and guidelines
            context_tables: Database table schema information
            context_joins: Table relationship information  
            user_query: User's natural language query
            database_type: Type of database (PostgreSQL, MySQL, etc.)
            
        Returns:
            Dictionary containing SQL query and metadata
        """
        try:
            # Create context and prompt
            context_string = self.create_context_string(
                context_rules, context_tables, context_joins, database_type
            )
            prompt = self.create_prompt(context_string, user_query)
            
            if self.debug:
                print("=== PROMPT ===")
                print(prompt)
                print("=== END PROMPT ===\n")
            
            # Get response from LLM
            response = self.llm.completion_json(
                user_prompt=prompt,
                verbose=self.debug,
                log_context={"task": "sql_generation", "query": user_query}
            )
            
            if self.debug:
                print("=== LLM RESPONSE ===")
                print(json.dumps(response, indent=2))
                print("=== END RESPONSE ===\n")
                
            return response
            
        except Exception as e:
            error_response = {
                "error": str(e),
                "sql_query": None,
                "explanation": f"Error occurred during SQL generation: {str(e)}",
                "tables_used": [],
                "assumptions": []
            }
            if self.debug:
                print(f"Error in generate_sql: {e}")
            return error_response


def test_sql_generator():
    """Test function for the SQL generator"""
    generator = SQLGenerator(model="anthropic/claude-sonnet-4-20250514", debug=True)
    
    # Sample context for testing
    rules = "### RULES\n- Use ILIKE for case-insensitive matching\n- Prefer simple queries"
    tables = "### TABLES\nusers (id, name, email)\norders (id, user_id, amount)"
    joins = "### JOINS\norders.user_id -> users.id"
    
    result = generator.generate_sql(
        context_rules=rules,
        context_tables=tables, 
        context_joins=joins,
        user_query="Find all users with orders over $100"
    )
    
    print("Generated SQL:", result.get('sql_query'))
    print("Explanation:", result.get('explanation'))


if __name__ == "__main__":
    test_sql_generator()
