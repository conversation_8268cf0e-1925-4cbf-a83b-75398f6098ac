#!/usr/bin/env python3
"""
CB_AUTO Database Context

Auto-generated database context for cb_auto database.
Generated from schema inspection of 4 tables.
"""

from db_context import get_context, extract_table_names, format_context_summary

# Database-specific rules and guidelines
CONTEXT_RULES = """### RULES
- Use PostgreSQL syntax and functions
- Use ILIKE for case-insensitive pattern matching
- Use proper JOINs based on foreign key relationships
- Consider using indexes for performance
- Use appropriate data type casting when needed
- Return valid PostgreSQL syntax
"""

# Database table schemas
CONTEXT_TABLES = """### TABLE_SCHEMAS

Table "django.cb16_organizations"
  uuid | text | NULL
  company_name | text | NULL
  type | text | NULL
  permalink | text | NULL
  cb_url | text | NULL
  rank | text | NULL
  created_at | text | NULL
  updated_at | text | NULL
  legal_name | text | NULL
  roles | text | NULL
  domain | text | NULL
  homepage_url | text | NULL
  country_code | text | NULL
  state_code | text | NULL
  region | text | NULL
  city | text | NULL
  address | text | NULL
  postal_code | text | NULL
  status | text | NULL
  short_description | text | NULL
  category_list | text | NULL
  category_group_list | text | NULL
  funding_rounds | text | NULL
  funding_total_usd | text | NULL
  total_funding | text | NULL
  total_funding_currency_code | text | NULL
  founded_on | text | NULL
  last_funding_on | text | NULL
  closed_on | text | NULL
  employee_count | text | NULL
  email | text | NULL
  phone | text | NULL
  facebook_url | text | NULL
  linkedin_url | text | NULL
  twitter_url | text | NULL
  logo_url | text | NULL
  alias1 | text | NULL
  alias2 | text | NULL
  alias3 | text | NULL
  primary_role | text | NULL
  num_exits | text | NULL

Table "django.cb16_funding_rounds"
  funding_round_uuid | text | NULL
  name | text | NULL
  type | text | NULL
  permalink | text | NULL
  cb_url | text | NULL
  rank | text | NULL
  created_at | text | NULL
  updated_at | text | NULL
  country_code | text | NULL
  state_code | text | NULL
  region | text | NULL
  city | text | NULL
  investment_type | text | NULL
  announced_on | text | NULL
  raised_amount_usd | text | NULL
  raised_amount | text | NULL
  raised_amount_currency_code | text | NULL
  post_money_valuation_usd | text | NULL
  post_money_valuation | text | NULL
  post_money_valuation_currency_code | text | NULL
  investor_count | text | NULL
  company_uuid | text | NULL
  company_name | text | NULL
  investor_uuids | text | NULL

Table "django.cb16_investments"
  uuid | text | NULL
  name | text | NULL
  type | text | NULL
  permalink | text | NULL
  cb_url | text | NULL
  rank | text | NULL
  created_at | text | NULL
  updated_at | text | NULL
  funding_round_uuid | text | NULL
  funding_round_name | text | NULL
  investor_uuid | text | NULL
  investor_name | text | NULL
  investor_type | text | NULL
  is_lead_investor | text | NULL

Table "django.cb16_investors"
  uuid | text | NULL
  investor_name | text | NULL
  type | text | NULL
  permalink | text | NULL
  cb_url | text | NULL
  rank | text | NULL
  created_at | text | NULL
  updated_at | text | NULL
  roles | text | NULL
  domain | text | NULL
  country_code | text | NULL
  state_code | text | NULL
  region | text | NULL
  city | text | NULL
  investor_type | text | NULL
  investment_count | text | NULL
  total_funding_usd | text | NULL
  total_funding | text | NULL
  total_funding_currency_code | text | NULL
  founded_on | text | NULL
  closed_on | text | NULL
  facebook_url | text | NULL
  linkedin_url | text | NULL
  twitter_url | text | NULL
  logo_url | text | NULL

"""

# Table relationships and joins
CONTEXT_JOINS = """### TABLE_JOINS

No foreign key relationships found.
"""

# Database metadata
DATABASE_TYPE = "postgresql"
SCHEMA_NAME = "django"

# Common query patterns for cb_auto
COMMON_QUERIES = {
    'record_count': "Count total records in tables",
    'table_info': "Get information about table structure",
    'relationship_queries': "Query data across related tables",
    'filtered_search': "Search and filter records"
}


def get_context():
    """Get cb_auto database context."""
    from db_context import get_context as get_db_context
    return get_db_context(CONTEXT_RULES, CONTEXT_TABLES, CONTEXT_JOINS, DATABASE_TYPE, SCHEMA_NAME)


def get_table_list():
    """Get list of cb_auto tables."""
    return extract_table_names(CONTEXT_TABLES, SCHEMA_NAME)


def get_common_queries():
    """Get cb_auto-specific query examples."""
    return COMMON_QUERIES


if __name__ == "__main__":
    context = get_context()
    print(format_context_summary(context))
    print(f"\nTables: {get_table_list()}")
    print(f"Common Queries: {list(get_common_queries().keys())}")
