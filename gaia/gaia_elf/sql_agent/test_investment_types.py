#!/usr/bin/env python3
"""
Test that investment types are properly included in the CrunchBase context.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

def test_investment_types_in_context():
    """Test that investment types are included in the context."""
    print("=== Testing Investment Types in Context ===\n")
    
    try:
        from dbs.cb.context import get_context, get_investment_types
        
        # Test the helper function
        investment_types = get_investment_types()
        print(f"✅ Investment types function works: {len(investment_types)} types")
        print(f"Sample types: {investment_types[:5]}")
        
        # Test that context includes the values
        context = get_context()
        tables_content = context['tables']
        
        # Check if investment types are in the context
        if 'INVESTMENT_TYPES' in tables_content:
            print("✅ Investment types section found in context")
        else:
            print("❌ Investment types section NOT found in context")
            return False
        
        # Check for specific investment types
        key_types = ['series_a', 'seed', 'angel', 'series_b']
        found_types = []
        for inv_type in key_types:
            if inv_type in tables_content:
                found_types.append(inv_type)
        
        print(f"✅ Found {len(found_types)}/{len(key_types)} key investment types in context")
        print(f"Found types: {found_types}")
        
        # Check total count
        if '28 distinct investment types' in tables_content:
            print("✅ Total count (28) mentioned in context")
        else:
            print("❌ Total count not found in context")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing investment types: {e}")
        return False

def test_context_structure():
    """Test the overall context structure."""
    print("\n=== Testing Context Structure ===\n")
    
    try:
        from dbs.cb.context import get_context
        
        context = get_context()
        
        # Check required fields
        required_fields = ['rules', 'tables', 'joins', 'database_type', 'schema_name']
        for field in required_fields:
            if field in context:
                print(f"✅ {field}: present")
            else:
                print(f"❌ {field}: missing")
                return False
        
        # Check content
        print(f"✅ Database type: {context['database_type']}")
        print(f"✅ Schema: {context['schema_name']}")
        
        # Check if tables content includes both schema and values
        tables_content = context['tables']
        if 'TABLE_SCHEMAS' in tables_content and 'INVESTMENT_TYPES' in tables_content:
            print("✅ Context includes both table schemas and investment types")
        else:
            print("❌ Context missing table schemas or investment types")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing context structure: {e}")
        return False

def show_sample_context():
    """Show a sample of the context content."""
    print("\n=== Sample Context Content ===\n")
    
    try:
        from dbs.cb.context import get_context
        
        context = get_context()
        tables_content = context['tables']
        
        # Find and show investment types section
        lines = tables_content.split('\n')
        in_investment_section = False
        investment_lines = []
        
        for line in lines:
            if 'INVESTMENT_TYPES' in line:
                in_investment_section = True
                investment_lines.append(line)
            elif in_investment_section:
                if line.strip() and not line.startswith('-') and not line.startswith('Available') and not line.startswith('Total'):
                    break
                investment_lines.append(line)
                if len(investment_lines) > 15:  # Limit output
                    investment_lines.append("... (truncated)")
                    break
        
        print("Investment Types Section:")
        for line in investment_lines:
            print(line)
        
        return True
        
    except Exception as e:
        print(f"❌ Error showing context: {e}")
        return False

def main():
    """Run all tests."""
    print("=== Investment Types Context Test ===\n")
    
    tests = [
        ("Investment Types in Context", test_investment_types_in_context),
        ("Context Structure", test_context_structure),
        ("Sample Context Content", show_sample_context)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n=== Test Results ===")
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed!")
        print("✅ Investment types are properly included in CrunchBase context")
        print("✅ SQL agent can now use investment type values in queries")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
