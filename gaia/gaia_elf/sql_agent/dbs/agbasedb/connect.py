#!/usr/bin/env python3
"""
AgBase Database Connection Configuration

Minimal configuration data for the AgBase database tables.
All boilerplate code is in db_connect.py.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_connect import get_db_config, test_connection_config, print_connection_info

# Default connection configuration for AgBase database (same as CB since same DB)
DEFAULT_AGBASEDB_CONFIG = {
    'type': 'postgresql',
    'host': 'localhost',
    'port': 15432,
    'database': 'agbase1',
    'user': 'postgres',
    'password': 'password',
    'schema': 'public'
}

# Environment variable mappings for configuration override
ENV_VAR_MAPPING = {
    'host': 'AGBASEDB_DB_HOST',
    'port': 'AGBASEDB_DB_PORT', 
    'database': 'AGBASEDB_DB_NAME',
    'user': 'AGBASEDB_DB_USER',
    'password': 'AGBASEDB_DB_PASSWORD',
    'schema': 'AGBASEDB_DB_SCHEMA'
}


def get_agbasedb_config(override_config=None):
    """Get AgBase database configuration."""
    return get_db_config(DEFAULT_AGBASEDB_CONFIG, ENV_VAR_MAPPING, override_config)


if __name__ == "__main__":
    config = get_agbasedb_config()
    print_connection_info(config, ENV_VAR_MAPPING)
    
    print(f"\n=== Connection Test ===")
    test_result = test_connection_config(config)
    if test_result['success']:
        print("✅ Connection successful!")
        print(f"Database version: {test_result['database_version']}")
    else:
        print("❌ Connection failed!")
        print(f"Error: {test_result['message']}")
