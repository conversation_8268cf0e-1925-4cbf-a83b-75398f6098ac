#!/usr/bin/env python3
"""
AGBASEDB Database Context

Auto-generated database context for agbasedb database.
Generated from schema inspection of 22 tables.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_context import get_context, extract_table_names, format_context_summary

# Database-specific rules and guidelines
CONTEXT_RULES = """### RULES
- Use PostgreSQL syntax and functions
- Use ILIKE for case-insensitive pattern matching
- Use proper JOINs based on foreign key relationships
- Consider using indexes for performance
- Use appropriate data type casting when needed
- Return valid PostgreSQL syntax

- companies:
  - usually filter by rec_inclusion='Y' ("included"), though
  sometimes we want to look at "excluded" ('N') and "unrated" ('-') or
  duplicate ('D')
  - org_role : default IN ('COMP', 'BOTH') , sometimes want 'INV'

- rounds:
   - usually want rec_inclusion!='N'
"""

# Database table schemas
CONTEXT_TABLES = """### TABLE_SCHEMAS

Table "agbase_agtechcategory"
                                           Table "django.agbase_agtechcategory"
       Column        |           Type           | Collation | Nullable |                      Default                      
---------------------+--------------------------+-----------+----------+---------------------------------------------------
 id                  | integer                  |           | not null | nextval('agbase_agtechcategory_id_seq'::regclass)
 deleted             | timestamp with time zone |           |          | 
 name                | character varying(60)    |           | not null | 
 slug                | character varying(60)    |           | not null | 
 description         | text                     |           |          | 
 concurrency_version | bigint                   |           | not null | 
 rec_creator_id      | integer                  |           |          | 
 rec_editor_last_id  | integer                  |           |          | 
 stream              | character varying(5)     |           | not null | 
 is_agrifoodtech     | boolean                  |           | not null | 


Table "agbase_chat"

Table "agbase_frview_incl_cache"

Table "agbase_frview_pre_tbl"

                                             Table "django.agbase_fundinground"
         Column         |           Type           | Collation | Nullable |                     Default                     
------------------------+--------------------------+-----------+----------+-------------------------------------------------
 id                     | integer                  |           | not null | nextval('agbase_fundinground_id_seq'::regclass)
 deleted                | timestamp with time zone |           |          | 
 rec_inclusion          | character varying(3)     |           | not null | 
 rec_inclusion_notes    | character varying(1000)  |           |          | 
 rec_quality            | character varying(10)    |           |          | 
 rec_quality_notes      | character varying(1000)  |           |          | 
 rec_src                | character varying(6)     |           |          | 
 rec_src_notes          | character varying(100)   |           |          | 
 rec_privacy            | character varying(100)   |           |          | 
 uuid                   | uuid                     |           | not null | 
 created_at             | timestamp with time zone |           | not null | 
 updated_at             | timestamp with time zone |           | not null | 
 annc_on                | date                     |           | not null | 
 amt_usd                | bigint                   |           |          | 
 cb_fr_code             | character varying(20)    |           |          | 
 cb_fr_type             | character varying(50)    |           |          | 
 investor_names         | character varying(3000)  |           |          | 
 investor_permalinks    | character varying(3000)  |           |          | 
 cb_uuid_org            | character varying(100)   |           |          | 
 cb_uuid_fr             | character varying(100)   |           |          | 
 fr_code_id             | integer                  |           | not null | 
 rec_creator_id         | integer                  |           |          | 
 rec_import_id          | integer                  |           |          | 
 company_id             | integer                  |           | not null | 
 lead_investor_id       | integer                  |           |          | 
 rec_src_link           | character varying(1000)  |           |          | 
 concurrency_version    | bigint                   |           | not null | 
 rec_editor_last_id     | integer                  |           |          | 
 rec_notetext           | text                     |           |          | 
 rec_needs_review       | boolean                  |           | not null | 
 rec_inclusion_pred     | double precision         |           |          | 
 cb_created_at          | timestamp with time zone |           |          | 
 cb_updated_at          | timestamp with time zone |           |          | 
 rec_dupe_id_id         | integer                  |           |          | 
 rec_dupe_note          | text                     |           |          | 
 rec_search_vector      | tsvector                 |           |          | 
 rec_search_text        | text                     |           |          | 
 extension_of_fr_id     | integer                  |           |          | 
 was_conv_note          | boolean                  |           | not null | 
 rec_dupe_prob          | double precision         |           |          | 
 ml_data                | jsonb                    |           |          | 
 ml_data2               | jsonb                    |           |          | 
 ml_when                | timestamp with time zone |           |          | 
 ml_when2               | timestamp with time zone |           |          | 
 rec_inclusion_pred2    | double precision         |           |          | 
 ml_data_web            | jsonb                    |           |          | 
 ml_when_web            | timestamp with time zone |           |          | 
 rec_inclusion_pred_web | double precision         |           |          | 

Table "agbase_fundingroundcode"

                                 Table "django.agbase_fundinground_investors"
     Column      |  Type   | Collation | Nullable |                          Default                          
-----------------+---------+-----------+----------+-----------------------------------------------------------
 id              | integer |           | not null | nextval('agbase_fundinground_investors_id_seq'::regclass)
 fundinground_id | integer |           | not null | 
 investor_id     | integer |           | not null | 

                                            Table "django.agbase_fundingroundcode"
        Column        |           Type           | Collation | Nullable |                       Default                       
----------------------+--------------------------+-----------+----------+-----------------------------------------------------
 id                   | integer                  |           | not null | nextval('agbase_fundingroundcode_id_seq'::regclass)
 deleted              | timestamp with time zone |           |          | 
 name                 | character varying(40)    |           | not null | 
 code                 | character varying(40)    |           | not null | 
 order                | integer                  |           |          | 
 concurrency_version  | bigint                   |           | not null | 
 rec_creator_id       | integer                  |           |          | 
 rec_editor_last_id   | integer                  |           |          | 
 report_round_code_id | integer                  |           |          | 

 

Table "agbase_label"

                                             Table "django.agbase_org"
         Column         |           Type           | Collation | Nullable |                Default                 
------------------------+--------------------------+-----------+----------+----------------------------------------
 id                     | integer                  |           | not null | nextval('agbase_org_id_seq'::regclass)
 deleted                | timestamp with time zone |           |          | 
 rec_inclusion          | character varying(3)     |           | not null | 
 rec_inclusion_notes    | character varying(1000)  |           |          | 
 rec_quality            | character varying(10)    |           |          | 
 rec_quality_notes      | character varying(1000)  |           |          | 
 rec_src                | character varying(6)     |           |          | 
 rec_src_notes          | character varying(100)   |           |          | 
 rec_privacy            | character varying(100)   |           |          | 
 uuid                   | uuid                     |           | not null | 
 created_at             | timestamp with time zone |           | not null | 
 updated_at             | timestamp with time zone |           | not null | 
 slug                   | character varying(127)   |           | not null | 
 cb_slug                | character varying(255)   |           |          | 
 cb_url                 | character varying(1200)  |           |          | 
 cb_uuid                | character varying(100)   |           |          | 
 org_class              | character varying(4)     |           | not null | 
 name                   | character varying(255)   |           | not null | 
 short_descr            | text                     |           |          | 
 descr                  | text                     |           |          | 
 website                | character varying(1001)  |           |          | 
 domain                 | character varying(302)   |           |          | 
 country                | character varying(2)     |           |          | 
 state_code             | character varying(127)   |           |          | 
 city                   | character varying(50)    |           |          | 
 url_linkedin           | character varying(1200)  |           |          | 
 url_logo               | character varying(1200)  |           |          | 
 url_facebook           | character varying(1200)  |           |          | 
 url_twitter            | character varying(1200)  |           |          | 
 affil_org              | character varying(100)   |           |          | 
 affil_org_uuid         | character varying(100)   |           |          | 
 affil_title            | character varying(100)   |           |          | 
 org_role               | character varying(6)     |           | not null | 
 cb_category_group_list | character varying(1000)  |           |          | 
 cb_category_list       | character varying(1000)  |           |          | 
 caf_version            | character varying(12)    |           |          | 
 caf_ts                 | timestamp with time zone |           |          | 
 caf_details            | text                     |           |          | 
 cb_investor_type       | character varying(1000)  |           |          | 
 date_founded           | date                     |           |          | 
 date_closed            | date                     |           |          | 
 category_id            | integer                  |           |          | 
 rec_creator_id         | integer                  |           |          | 
 rec_import_id          | integer                  |           |          | 
 rec_src_link           | character varying(1000)  |           |          | 
 concurrency_version    | bigint                   |           | not null | 
 rec_editor_last_id     | integer                  |           |          | 
 rec_notetext           | text                     |           |          | 
 ml_data                | jsonb                    |           |          | 
 tags_cache             | text                     |           |          | 
 rec_needs_review       | boolean                  |           | not null | 
 rec_inclusion_pred     | double precision         |           |          | 
 cb_created_at          | timestamp with time zone |           |          | 
 cb_updated_at          | timestamp with time zone |           |          | 
 affil_org_primary_id   | integer                  |           |          | 
 first_name             | character varying(255)   |           |          | 
 gender                 | character varying(4)     |           |          | 
 last_name              | character varying(255)   |           |          | 
 nicknames              | text                     |           |          | 
 oob_date               | date                     |           |          | 
 oob_lastchecked        | date                     |           |          | 
 oob_note               | text                     |           |          | 
 oob_prob               | double precision         |           |          | 
 rec_dupe_id_id         | integer                  |           |          | 
 rec_dupe_note          | text                     |           |          | 
 rec_search_vector      | tsvector                 |           |          | 
 rec_search_text        | text                     |           |          | 
 url_dealroom           | character varying(1200)  |           |          | 
 agtel_score            | double precision         |           |          | 
 gend_f                 | integer                  |           |          | 
 gend_m                 | integer                  |           |          | 
 gend_n                 | integer                  |           |          | 
 gend_ratio_f           | double precision         |           |          | 
 is_founder             | boolean                  |           |          | 
 leadership_team_size   | integer                  |           |          | 
 more_keywords          | text                     |           |          | 
 alt_names              | text                     |           |          | 
 rec_dupe_prob          | double precision         |           |          | 
 ml_when                | timestamp with time zone |           |          | 
 gend_updated_at        | timestamp with time zone |           |          | 
 ml_data2               | jsonb                    |           |          | 
 ml_when2               | timestamp with time zone |           |          | 
 rec_inclusion_pred2    | double precision         |           |          | 
 ml_data_web            | jsonb                    |           |          | 
 ml_when_web            | timestamp with time zone |           |          | 
 rec_inclusion_pred_web | double precision         |           |          | 
 report_end_date        | date                     |           |          | 
 report_start_date      | date                     |           |          | 
Indexes:

Table "agbase_org_affil_org_all"

Table "agbase_org_alltags"

Table "agbase_orgset"

Table "agbase_orgset_orgs"

Table "agbase_org_tags"

Table "agbase_tagtree"

Table "agbase_tagtree_labels"

Table "agbase_tagtree_virtual_children"

Table "agsearch_ascontext"

Table "agsearch_asdoc"

Table "agsearch_asquery"

Table "agsearch_asrating"

Table "agsearch_company_status"

"""

# Table relationships and joins
CONTEXT_JOINS = """### TABLE_JOINS

digraph FKs {
  "agbase_fundinground_investors" -> "agbase_fundinground" [
    label="fundinground_id → id"
  ];
  "agbase_fundinground_investors" -> "agbase_org" [
    label="investor_id → id"
  ];
  "agbase_fundinground" -> "agbase_org" [
    label="company_id → id"
  ];
  "agbase_fundinground" -> "agbase_org" [
    label="lead_investor_id → id"
  ];
  "agbase_fundinground" -> "agbase_fundingroundcode" [
    label="fr_code_id → id"
  ];
  "agbase_org" -> "agbase_agtechcategory" [
    label="category_id → id"
  ];
  "agbase_org" -> "agbase_org" [
    label="affil_org_primary_id → id"
  ];
}

### RELATIONSHIP DETAILS:
- agbase_org: Core organization/company data (companies, investors, people)
- agbase_fundinground: Funding rounds linked to companies via company_id
- agbase_fundinground_investors: Many-to-many relationship between funding rounds and investors
- agbase_fundingroundcode: Lookup table for funding round types/codes
- agbase_agtechcategory: Categories for organizations

### COMMON JOIN PATTERNS:
1. Organizations + Funding Rounds:
   FROM agbase_org org
   JOIN agbase_fundinground fr ON fr.company_id = org.id

2. Funding Rounds + Investors:
   FROM agbase_fundinground fr
   JOIN agbase_fundinground_investors fri ON fri.fundinground_id = fr.id
   JOIN agbase_org investor ON fri.investor_id = investor.id

3. Complete Funding Data:
   FROM agbase_org company
   JOIN agbase_fundinground fr ON fr.company_id = company.id
   JOIN agbase_fundinground_investors fri ON fri.fundinground_id = fr.id
   JOIN agbase_org investor ON fri.investor_id = investor.id
   JOIN agbase_fundingroundcode frc ON fr.fr_code_id = frc.id

4. Organizations + Categories:
   FROM agbase_org org
   JOIN agbase_agtechcategory cat ON org.category_id = cat.id

5. Organization Affiliations:
   FROM agbase_org org
   JOIN agbase_org parent ON org.affil_org_primary_id = parent.id
"""

# Important data values for context
CONTEXT_VALUES = """
### IMPORTANT VALUES

#### Organization Roles (org_role in agbase_org):
- 'COMP' - Company/Organization
- 'INV' - Investor
- 'BOTH' - both

#### Organization Classes (org_class in agbase_org):
- 'ORG' - Company
- 'IND' - Individual

#### Record Inclusion Status (rec_inclusion):
- 'Y' - Included record
- 'N' - Excluded record
- 'D' - Duplicate
- '-' - Unrated

#### Funding Round Codes (cb_fr_type in agbase_fundinground):
Similar to CrunchBase investment types: seed, series_a, series_b, angel, etc.

#### Key ID Relationships:
- company_id in agbase_fundinground → id in agbase_org (where org_role='comp')
- investor_id in agbase_fundinground_investors → id in agbase_org (where org_role='inv')
- lead_investor_id in agbase_fundinground → id in agbase_org (where org_role='inv')
- category_id in agbase_org → id in agbase_agtechcategory
"""

# Database metadata
DATABASE_TYPE = "postgresql"
SCHEMA_NAME = "public"

# Common query patterns for agbasedb
COMMON_QUERIES = {
    'record_count': "Count total records in tables",
    'table_info': "Get information about table structure",
    'relationship_queries': "Query data across related tables",
    'filtered_search': "Search and filter records"
}


def get_context():
    """Get agbasedb database context."""
    from db_context import get_context as get_db_context
    # Combine tables and values for complete context
    combined_tables = CONTEXT_TABLES + "\n" + CONTEXT_VALUES
    return get_db_context(CONTEXT_RULES, combined_tables, CONTEXT_JOINS, DATABASE_TYPE, SCHEMA_NAME)


def get_table_list():
    """Get list of agbasedb tables."""
    return extract_table_names(CONTEXT_TABLES, SCHEMA_NAME)


def get_common_queries():
    """Get agbasedb-specific query examples."""
    return COMMON_QUERIES


def get_org_roles():
    """Get list of organization roles."""
    return ['comp', 'inv', 'pers']


def get_org_classes():
    """Get list of organization classes."""
    return ['comp', 'inv', 'pers', 'acad']


def get_inclusion_statuses():
    """Get list of record inclusion statuses."""
    return ['yes', 'no', 'may']


def get_key_relationships():
    """Get key foreign key relationships for AgBase."""
    return {
        'company_funding': 'agbase_fundinground.company_id → agbase_org.id',
        'funding_investors': 'agbase_fundinground_investors.fundinground_id → agbase_fundinground.id',
        'investor_details': 'agbase_fundinground_investors.investor_id → agbase_org.id',
        'lead_investor': 'agbase_fundinground.lead_investor_id → agbase_org.id',
        'funding_codes': 'agbase_fundinground.fr_code_id → agbase_fundingroundcode.id',
        'org_categories': 'agbase_org.category_id → agbase_agtechcategory.id',
        'org_affiliations': 'agbase_org.affil_org_primary_id → agbase_org.id'
    }


if __name__ == "__main__":
    context = get_context()
    print(format_context_summary(context))
    print(f"\nTables: {get_table_list()}")
    print(f"Common Queries: {list(get_common_queries().keys())}")
    print(f"Organization Roles: {get_org_roles()}")
    print(f"Organization Classes: {get_org_classes()}")
    print(f"Key Relationships: {len(get_key_relationships())} defined")
    print(f"Sample Relationships: {list(get_key_relationships().keys())[:3]}...")
