#!/usr/bin/env python3
"""
CrunchBase Database Connection Configuration

Minimal configuration data for the CrunchBase (CB16) database.
All boilerplate code is in db_connect.py.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_connect import get_db_config, test_connection_config, print_connection_info

# Default connection configuration for CrunchBase database
DEFAULT_CB_CONFIG = {
    'type': 'postgresql',
    'host': 'localhost',
    'port': 15432,
    'database': 'agbase1',
    'user': 'postgres',
    'password': 'password',
    'schema': 'django'
}

# Environment variable mappings for configuration override
ENV_VAR_MAPPING = {
    'host': 'CB_DB_HOST',
    'port': 'CB_DB_PORT',
    'database': 'CB_DB_NAME',
    'user': 'CB_DB_USER',
    'password': 'CB_DB_PASSWORD',
    'schema': 'CB_DB_SCHEMA'
}


def get_cb_db_config(override_config=None):
    """Get CrunchBase database configuration."""
    return get_db_config(DEFAULT_CB_CONFIG, ENV_VAR_MAPPING, override_config)


if __name__ == "__main__":
    config = get_cb_db_config()
    print_connection_info(config, ENV_VAR_MAPPING)

    print(f"\n=== Connection Test ===")
    test_result = test_connection_config(config)
    if test_result['success']:
        print("✅ Connection successful!")
        print(f"Database version: {test_result['database_version']}")
    else:
        print("❌ Connection failed!")
        print(f"Error: {test_result['message']}")
