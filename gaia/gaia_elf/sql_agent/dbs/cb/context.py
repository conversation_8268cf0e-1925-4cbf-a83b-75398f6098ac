#!/usr/bin/env python3
"""
CrunchBase Database Context

Minimal schema context data for the CrunchBase (CB16) database.
All boilerplate code is in db_context.py.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from db_context import get_context, extract_table_names, format_context_summary

# Database-specific rules and guidelines
CONTEXT_RULES = """
### RULES
- Try to do things the most efficient way possible, and/or the most simple way
- Prefer = to ILIKE, when using ILIKE try to avoid starting with '%' if you can
- All columns are of type 'text' - use appropriate string operations and casting
- Use ILIKE for case-insensitive pattern matching when needed
- Consider domain matching for company identification (e.g., 'facebook.com' in domain column)
- Use proper JOINs based on the relationship diagram provided
- Return valid PostgreSQL syntax
- Tables are in the 'django' schema, so use django.table_name format

### TERMINOLOGY NOTES
- When users mention "round code", "round type", or "funding type", they are referring to the 'investment_type' column in django.cb16_funding_rounds
- The 'investment_type' column contains values like 'series_a', 'seed', 'angel', etc. (see INVESTMENT_TYPES section below)
- Do not confuse with the 'type' column which has different meaning
"""

# Database table schemas
CONTEXT_TABLES = """
### TABLE_SCHEMAS

                  Table "django.cb16_organizations"
           Column            | Type | Collation | Nullable | Default 
-----------------------------+------+-----------+----------+---------
 uuid                        | text |           |          | 
 company_name                | text |           |          | 
 type                        | text |           |          | 
 permalink                   | text |           |          | 
 cb_url                      | text |           |          | 
 rank                        | text |           |          | 
 created_at                  | text |           |          | 
 updated_at                  | text |           |          | 
 legal_name                  | text |           |          | 
 roles                       | text |           |          | 
 domain                      | text |           |          | 
 homepage_url                | text |           |          | 
 country_code                | text |           |          | 
 state_code                  | text |           |          | 
 region                      | text |           |          | 
 city                        | text |           |          | 
 address                     | text |           |          | 
 postal_code                 | text |           |          | 
 status                      | text |           |          | 
 short_description           | text |           |          | 
 category_list               | text |           |          | 
 category_group_list         | text |           |          | 
 funding_rounds              | text |           |          | 
 funding_total_usd           | text |           |          | 
 total_funding               | text |           |          | 
 total_funding_currency_code | text |           |          | 
 founded_on                  | text |           |          | 
 last_funding_on             | text |           |          | 
 closed_on                   | text |           |          | 
 employee_count              | text |           |          | 
 email                       | text |           |          | 
 phone                       | text |           |          | 
 facebook_url                | text |           |          | 
 linkedin_url                | text |           |          | 
 twitter_url                 | text |           |          | 
 logo_url                    | text |           |          | 
 alias1                      | text |           |          | 
 alias2                      | text |           |          | 
 alias3                      | text |           |          | 
 primary_role                | text |           |          | 
 num_exits                   | text |           |          | 

 
agbase1=# \d cb16_funding_rounds 
                     Table "django.cb16_funding_rounds"
               Column               | Type | Collation | Nullable | Default 
------------------------------------+------+-----------+----------+---------
 funding_round_uuid                 | text |           |          | 
 name                               | text |           |          | 
 type                               | text |           |          | 
 permalink                          | text |           |          | 
 cb_url                             | text |           |          | 
 rank                               | text |           |          | 
 created_at                         | text |           |          | 
 updated_at                         | text |           |          | 
 country_code                       | text |           |          | 
 state_code                         | text |           |          | 
 region                             | text |           |          | 
 city                               | text |           |          | 
 investment_type                    | text |           |          | 
 announced_on                       | text |           |          | 
 raised_amount_usd                  | text |           |          | 
 raised_amount                      | text |           |          | 
 raised_amount_currency_code        | text |           |          | 
 post_money_valuation_usd           | text |           |          | 
 post_money_valuation               | text |           |          | 
 post_money_valuation_currency_code | text |           |          | 
 investor_count                     | text |           |          | 
 company_uuid                       | text |           |          | 
 company_name                       | text |           |          | 
 investor_uuids                     | text |           |          | 


 agbase1=# \d cb16_investments
              Table "django.cb16_investments"
       Column       | Type | Collation | Nullable | Default 
--------------------+------+-----------+----------+---------
 uuid               | text |           |          | 
 name               | text |           |          | 
 type               | text |           |          | 
 permalink          | text |           |          | 
 cb_url             | text |           |          | 
 rank               | text |           |          | 
 created_at         | text |           |          | 
 updated_at         | text |           |          | 
 funding_round_uuid | text |           |          | 
 funding_round_name | text |           |          | 
 investor_uuid      | text |           |          | 
 investor_name      | text |           |          | 
 investor_type      | text |           |          | 
 is_lead_investor   | text |           |          | 

 agbase1=# \d cb16_investors
                    Table "django.cb16_investors"
           Column            | Type | Collation | Nullable | Default 
-----------------------------+------+-----------+----------+---------
 uuid                        | text |           |          | 
 investor_name               | text |           |          | 
 type                        | text |           |          | 
 permalink                   | text |           |          | 
 cb_url                      | text |           |          | 
 rank                        | text |           |          | 
 created_at                  | text |           |          | 
 updated_at                  | text |           |          | 
 roles                       | text |           |          | 
 domain                      | text |           |          | 
 country_code                | text |           |          | 
 state_code                  | text |           |          | 
 region                      | text |           |          | 
 city                        | text |           |          | 
 investor_type               | text |           |          | 
 investment_count            | text |           |          | 
 total_funding_usd           | text |           |          | 
 total_funding               | text |           |          | 
 total_funding_currency_code | text |           |          | 
 founded_on                  | text |           |          | 
 closed_on                   | text |           |          | 
 facebook_url                | text |           |          | 
 linkedin_url                | text |           |          | 
 twitter_url                 | text |           |          | 
 logo_url                    | text |           |          |

"""

# Important data values for context
CONTEXT_VALUES = """
### INVESTMENT_TYPES
Available investment_type values in django.cb16_funding_rounds:
(Note: When users say "round code", "round type", or "funding type", they mean this investment_type column)
- seed
- private_equity
- series_g
- series_c
- series_b
- series_e
- angel
- series_a
- pre_seed
- series_unknown
- post_ipo_secondary
- non_equity_assistance
- series_i
- corporate_round
- debt_financing
- undisclosed
- equity_crowdfunding
- series_d
- grant
- series_f
- secondary_market
- product_crowdfunding
- post_ipo_debt
- post_ipo_equity
- convertible_note
- initial_coin_offering
- series_j
- series_h

Total: 28 distinct investment types
"""

# Table relationships and joins
CONTEXT_JOINS = """
### TABLE_JOINS

digraph FKs {
  "django.cb16_funding_rounds" -> "django.cb16_organizations" [
    label="company_uuid → uuid"
  ];
  "django.cb16_investments" -> "django.cb16_funding_rounds" [
    label="funding_round_uuid → funding_round_uuid"
  ];
  "django.cb16_investments" -> "django.cb16_investors" [
    label="investor_uuid → uuid"
  ];
}

### RELATIONSHIP DETAILS:
- cb16_organizations: Core company/organization data
- cb16_funding_rounds: Funding rounds linked to organizations via company_uuid
- cb16_investments: Individual investment records linking rounds to investors
- cb16_investors: Investor information and details

### COMMON JOIN PATTERNS:
1. Organizations + Funding Rounds:
   FROM django.cb16_funding_rounds fr
   JOIN django.cb16_organizations org ON fr.company_uuid = org.uuid

2. Funding Rounds + Investments + Investors:
   FROM django.cb16_funding_rounds fr
   JOIN django.cb16_investments inv ON inv.funding_round_uuid = fr.funding_round_uuid
   JOIN django.cb16_investors investor ON inv.investor_uuid = investor.uuid

3. Organizations + All Investment Data:
   FROM django.cb16_organizations org
   JOIN django.cb16_funding_rounds fr ON fr.company_uuid = org.uuid
   JOIN django.cb16_investments inv ON inv.funding_round_uuid = fr.funding_round_uuid
   JOIN django.cb16_investors investor ON inv.investor_uuid = investor.uuid
"""

# Database metadata
DATABASE_TYPE = "PostgreSQL"
SCHEMA_NAME = "django"

# Common query patterns for CrunchBase
COMMON_QUERIES = {
    'company_funding_count': "Count funding rounds for a specific company",
    'investor_portfolio': "Find all companies an investor has invested in",
    'funding_by_location': "Find companies by geographic location",
    'investment_amounts': "Analyze funding amounts and valuations",
    'investor_activity': "Track investor activity and patterns"
}


def get_context():
    """Get CrunchBase database context."""
    from db_context import get_context as get_db_context
    # Combine tables and values for complete context
    combined_tables = CONTEXT_TABLES + "\n" + CONTEXT_VALUES
    return get_db_context(CONTEXT_RULES, combined_tables, CONTEXT_JOINS, DATABASE_TYPE, SCHEMA_NAME)


def get_table_list():
    """Get list of CrunchBase tables."""
    return extract_table_names(CONTEXT_TABLES, SCHEMA_NAME)


def get_common_queries():
    """Get CrunchBase-specific query examples."""
    return COMMON_QUERIES


def get_investment_types():
    """Get list of available investment types."""
    return [
        'seed', 'private_equity', 'series_g', 'series_c', 'series_b', 'series_e',
        'angel', 'series_a', 'pre_seed', 'series_unknown', 'post_ipo_secondary',
        'non_equity_assistance', 'series_i', 'corporate_round', 'debt_financing',
        'undisclosed', 'equity_crowdfunding', 'series_d', 'grant', 'series_f',
        'secondary_market', 'product_crowdfunding', 'post_ipo_debt', 'post_ipo_equity',
        'convertible_note', 'initial_coin_offering', 'series_j', 'series_h'
    ]


if __name__ == "__main__":
    context = get_context()
    print(format_context_summary(context))
    print(f"\nTables: {get_table_list()}")
    print(f"Common Queries: {list(get_common_queries().keys())}")
    print(f"Investment Types: {len(get_investment_types())} available")
    print(f"Sample Investment Types: {get_investment_types()[:5]}...")
