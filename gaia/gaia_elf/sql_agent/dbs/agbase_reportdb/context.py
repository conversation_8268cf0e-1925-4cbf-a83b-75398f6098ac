#!/usr/bin/env python3
"""
AGBASEDB Report 2025 – **Ultra‑Minimal Core‑View Context**
========================================================

Objective
---------
Expose the *smallest* set of raw, row‑level facts that lets an analyst (human or
LLM) reproduce **all** other ireport.2025all views with ad‑hoc SQL.  Every other
helper slice (upstream/downstream, quarterly roll‑ups, category splits, country
breakdowns, etc.) can be re‑created via filtering and `GROUP BY`.
"""

import sys, os
sys.path.append(os.path.join(os.path.dirname(__file__), "../.."))

from db_context import (
    get_context as _base_ctx,
    extract_table_names,
    format_context_summary,
)

# ──────────────────────────────────────────────────────────────────────────────
# RULES
# ──────────────────────────────────────────────────────────────────────────────

CONTEXT_RULES = """### RULES
- PostgreSQL 16 dialect; **views are read‑only**
- Use ILIKE for case‑insensitive pattern matches
- `stream` codes: 'UP' (Upstream) / 'DN' (Downstream)
- `annc_on_year` : "2025"
- `annc_on_half` : "2025-H1"
- `annc_on_quarter` : "2025-Q2"
- report_code : (in order of report_code_order) ["Seed","A","B","C","D","Late","Debt"]
- `value_chain_top` buckets supply‑chain segment (e.g. Onfarm)
- Derive aggregates yourself: `GROUP BY` quarter, year, country, category, etc.
"""

# ──────────────────────────────────────────────────────────────────────────────
# SINGLE SOURCE‑OF‑TRUTH FACT TABLES
# ──────────────────────────────────────────────────────────────────────────────

CONTEXT_TABLES = """### TABLE_SCHEMAS

View `agbase_frview_incl`  ★ the *master* history view
    Company: name, slug, category_name, value_chain_top, stream, org_id, country, state_code, geo_region, geo_sub_region, geo_country_name, geo_country_3
    Round: amt_usd, annc_on_year, annc_on_half, annc_on_quarter, report_code, annc_on_utc    

View `agbase_invmtview` ★ investor‑round facts
    Company: (same as above )
    Round: (same as above)
    Investor: inv_geo_country_3, inv_geo_region,inv_name, inv_slug, inv_geo_sub_region, inv_cb_investor_type,


"""

'''

'''

# ──────────────────────────────────────────────────────────────────────────────
# HOW TO REBUILD THE OTHER VIEWS (examples)
# ──────────────────────────────────────────────────────────────────────────────

REBUILD_SNIPPETS = {
    "upstream_slice": (
        "-- Upstream all time rows\n"
        "SELECT * FROM agbase_frview_incl WHERE stream = 'UP';"
    ),
    "quarterly_totals": (
        "-- Quarterly sums all time\n"
        "SELECT annc_on_quarter   AS quarter,\n"
        "       SUM(amt_usd)      AS sum_usd,\n"
        "       COUNT(*)          AS num_deals\n"
        "FROM   agbase_frview_incl\n"
        "GROUP  BY annc_on_quarter\n"
        "ORDER  BY quarter;"
    ),
    "country_share": (
        "WITH total AS (SELECT SUM(amt_usd) AS s FROM agbase_frview_incl)\n"
        "SELECT country, SUM(amt_usd)                      AS sum_usd,\n"
        "       SUM(amt_usd)/ (SELECT s FROM total)        AS sum_ratio\n"
        "FROM   agbase_frview_incl\n"
        "GROUP  BY country\n"
        "ORDER  BY sum_usd DESC;"
    ),
}

# ──────────────────────────────────────────────────────────────────────────────
# ENUMS / CONSTANTS
# ──────────────────────────────────────────────────────────────────────────────

CONTEXT_VALUES = """### IMPORTANT VALUES

Streams: 'UP', 'DN'
Value‑chain buckets (value_chain_top): Onfarm, Midstream, Consumer, SupplyChain …
Report codes (report_code): Seed, A, B, C, D, Debt, Late, Needs Review …
"""

DATABASE_TYPE = "postgresql"
SCHEMA_NAME   = "public"

# ──────────────────────────────────────────────────────────────────────────────
# Helper wrappers (unchanged external contract)
# ──────────────────────────────────────────────────────────────────────────────

def get_context():
    return _base_ctx(CONTEXT_RULES + "\n", CONTEXT_TABLES + "\n" + CONTEXT_VALUES,
                     "", DATABASE_TYPE, SCHEMA_NAME)

def get_table_list():
    return extract_table_names(CONTEXT_TABLES, SCHEMA_NAME)


def get_common_queries():
    return REBUILD_SNIPPETS

# Compatibility helpers (kept, though not central here)

def get_org_roles():
    return ["ORG", "PERS"]

def get_org_classes():
    return ["COMP", "INV", "BOTH"]

def get_inclusion_statuses():
    return ["Y", "N", "D", "-"]

def get_key_relationships():
    return {
        "investor_link": "agbase_invmtview.fr_id → agbase_frview_incl.fr_id",
    }

# Debug runner
if __name__ == "__main__":
    ctx = get_context()
    print(format_context_summary(ctx))
    print("\nCore Views:", get_table_list())
    print("Example rebuilds:")
    for k, q in REBUILD_SNIPPETS.items():
        print(f"\n-- {k}\n{q}\n")
