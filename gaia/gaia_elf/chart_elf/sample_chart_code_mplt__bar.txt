import matplotlib.pyplot as plt
import pandas as pd

# --- CONFIGURATION: Chart size ---
size = 'small'   # choose: 'small', 'med', 'large'

# --- Size presets ---
presets = {
    'small': {
        'figsize': (4, 3),
        'fontsize': 11,
        'title_fontsize': 13,
        'tick_fontsize': 10,
        'annotate': False,
        'xlabel': True,
        'show_source': False
    },
    'med': {
        'figsize': (8, 5),
        'fontsize': 14,
        'title_fontsize': 18,
        'tick_fontsize': 13,
        'annotate': True,
        'xlabel': True,
        'show_source': True
    },
    'large': {
        'figsize': (12, 7),
        'fontsize': 18,
        'title_fontsize': 24,
        'tick_fontsize': 16,
        'annotate': True,
        'xlabel': True,
        'show_source': True
    }
}
p = presets[size]

# --- Data ---
bar_data = pd.DataFrame({
    'country': ['US', 'China', 'Japan', 'Germany', 'India', 'UK', 'France'],
    'value': [21.4, 14.3, 5.1, 3.8, 2.9, 2.8, 2.7]
})

colors = ['#212529', '#495057', '#7a8b74', '#6b7a8b', '#9b7d6b', '#b89968', '#6c757d']

fig, ax = plt.subplots(figsize=p['figsize'])

# --- Plot ---
ax.barh(bar_data['country'], bar_data['value'], color=colors, alpha=0.87)

ax.set_title("Top 7 World Economies (GDP)", fontsize=p['title_fontsize'], fontweight='bold', loc='left', pad=8)
if p['xlabel']:
    ax.set_xlabel('Trillion $', fontsize=p['fontsize'])

# --- Annotations (optional, only for med/large) ---
if p['annotate']:
    for i, (country, value) in enumerate(zip(bar_data['country'], bar_data['value'])):
        ax.text(value + 0.2, i, f"{value:.1f}", va='center', fontsize=p['fontsize']-2, color='#212529')

# --- Spines and ticks ---
for spine in ['right', 'top', 'left']:
    ax.spines[spine].set_visible(False)
ax.tick_params(axis='x', labelsize=p['tick_fontsize'], length=0, colors='#495057')
ax.tick_params(axis='y', labelsize=p['tick_fontsize'], length=0, colors='#495057')
ax.grid(False)
ax.set_ylabel('')

# --- Optional source annotation ---
if p['show_source']:
    fig.text(0.01, 0.01, "Source: World Bank", fontsize=max(p['fontsize']-3, 8),
             color='#6c757d', style='italic', ha='left')

plt.tight_layout(pad=0.5)

plt.savefig(f"chart_{size}.png", dpi=200, bbox_inches='tight', facecolor='white')
plt.show()
