import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from matplotlib.dates import DateFormatter, YearLocator

# ---- CHART SIZE PRESET ----
size = 'small'  # choose: 'small', 'med', 'large'

presets = {
    'small': {
        'figsize': (4, 3),
        'title_fontsize': 11,
        'legend_fontsize': 8,
        'tick_fontsize': 8,
        'linewidth': 1.5,
        'axhlinewidth': 1,
        'ncol_legend': 1,
        'show_source': False
    },
    'med': {
        'figsize': (8, 5),
        'title_fontsize': 16,
        'legend_fontsize': 11,
        'tick_fontsize': 12,
        'linewidth': 2.5,
        'axhlinewidth': 1.4,
        'ncol_legend': 2,
        'show_source': True
    },
    'large': {
        'figsize': (12, 7),
        'title_fontsize': 20,
        'legend_fontsize': 14,
        'tick_fontsize': 15,
        'linewidth': 3.5,
        'axhlinewidth': 2,
        'ncol_legend': 2,
        'show_source': True
    }
}
P = presets[size]

# ---- COLOR PALETTE ----
sophisticated_colors = {
    'charcoal': '#212529',
    'slate': '#495057',
    'steel': '#6c757d',
    'sage': '#7a8b74',
    'clay': '#9b7d6b',
    'ochre': '#b89968',
    'dusty_blue': '#6b7a8b',
    'mauve': '#8b6b7a',
    'text_primary': '#212529',
    'text_secondary': '#6c757d',
    'border': '#ced4da',
    'grid': '#e9ecef',
    'background': '#f4f5ef',
    'highlight': '#fcf8e3',
    'emphasis': '#000000',
    'white': '#ffffff'
}

# ---- DATA GENERATION ----
np.random.seed(42)
dates = pd.date_range('2020-01-01', '2023-12-31', freq='M')
data = pd.DataFrame({
    'date': dates,
    'Series_1': 100 + np.cumsum(np.random.randn(len(dates)) * 0.5),
    'Series_2': 95 + np.cumsum(np.random.randn(len(dates)) * 0.7),
    'Series_3': 98 + np.cumsum(np.random.randn(len(dates)) * 0.4),
    'Series_4': 96 + np.cumsum(np.random.randn(len(dates)) * 0.6)
})

# ---- FIGURE SETUP ----
fig, ax = plt.subplots(figsize=P['figsize'])
fig.patch.set_facecolor(sophisticated_colors['background'])
ax.set_facecolor(sophisticated_colors['white'])

# ---- PLOT LINES ----
ax.plot(data['date'], data['Series_1'],
        color=sophisticated_colors['charcoal'], linewidth=P['linewidth'],
        label='Primary Series', alpha=0.9)
ax.plot(data['date'], data['Series_2'],
        color=sophisticated_colors['sage'], linewidth=P['linewidth'],
        label='Secondary Series', alpha=0.9)
ax.plot(data['date'], data['Series_3'],
        color=sophisticated_colors['dusty_blue'], linewidth=P['linewidth'],
        label='Tertiary Series', alpha=0.9)
ax.plot(data['date'], data['Series_4'],
        color=sophisticated_colors['ochre'], linewidth=P['linewidth'],
        label='Quaternary Series', alpha=0.9)

# ---- BASELINE ----
ax.axhline(y=100, color=sophisticated_colors['text_primary'],
           linewidth=P['axhlinewidth'], alpha=0.8, zorder=0)

# ---- TITLE ----
ax.set_title('Sophisticated Data Visualization\nQuarterly performance metrics, indexed',
             fontsize=P['title_fontsize'],
             color=sophisticated_colors['text_primary'],
             fontweight='bold', pad=10, loc='left')

# ---- SPINES, GRID ----
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_linewidth(1.7)
ax.spines['bottom'].set_color(sophisticated_colors['text_primary'])
ax.grid(True, axis='y', alpha=0.3, color=sophisticated_colors['grid'], linewidth=1)
ax.set_axisbelow(True)

# ---- X AXIS: Years ----
years = YearLocator()
years_fmt = DateFormatter('%Y')
ax.xaxis.set_major_locator(years)
ax.xaxis.set_major_formatter(years_fmt)

# ---- TICKS ----
ax.tick_params(axis='x', labelsize=P['tick_fontsize'],
               colors=sophisticated_colors['text_secondary'],
               bottom=True, top=False)
ax.tick_params(axis='y', labelsize=P['tick_fontsize'],
               colors=sophisticated_colors['text_secondary'],
               left=False, right=False)

# ---- AXIS LABELS (minimal) ----
ax.set_ylabel('')
ax.set_xlabel('')

# ---- LEGEND ----
legend = ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=False,
                   fontsize=P['legend_fontsize'], ncol=P['ncol_legend'], columnspacing=1.5)
legend.get_frame().set_facecolor('white')
legend.get_frame().set_edgecolor(sophisticated_colors['border'])
legend.get_frame().set_alpha(0.9)

# ---- SOURCE ANNOTATION (optional) ----
if P['show_source']:
    fig.text(0.02, 0.02, "Source: Internal Analytics Dashboard",
             fontsize=max(P['legend_fontsize']-2, 8),
             color=sophisticated_colors['text_secondary'],
             style='italic', ha='left')

plt.tight_layout()
plt.subplots_adjust(bottom=0.14, left=0.09, right=0.97, top=0.90)

plt.savefig(f"timeseries_{size}.png", dpi=180, bbox_inches='tight', facecolor=sophisticated_colors['background'])
plt.show()
