#!/usr/bin/env python3
"""
Test script for simple_chart SVG generation using matplotlib
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))

from simple_chart_elf import simple_chart

# Test the function structure without dependencies
def test_function_structure():
    """Test that the simple_chart function is properly defined"""
    try:
        from simple_chart_elf import simple_chart
        print("✅ Function imported successfully")

        # Check function signature
        import inspect
        sig = inspect.signature(simple_chart)
        print(f"✅ Function signature: {sig}")

        # Check docstring
        if simple_chart.__doc__:
            print("✅ Function has docstring")
        else:
            print("⚠️  Function missing docstring")

        return True
    except Exception as e:
        print(f"❌ Error importing function: {e}")
        return False



def test_svg_chart(test_data, chart_spec):
    """Test SVG chart generation with sample data using matplotlib"""

    print("Testing simple_chart SVG generation with matplotlib...")
    print("Input data:")
    print(test_data[:200] + "..." if len(test_data) > 200 else test_data)
    print(f"\nChart specification: {chart_spec}")
    print("\n" + "="*50 + "\n")

    try:
        # Generate the SVG chart (default format is now SVG)
        result = simple_chart(test_data, chart_spec, output_format="svg")

        print("Chart generation completed!")
        print(f"Format: {result['format']}")
        print(f"Chart type: {result['chart_type']}")
        print(f"SVG content length: {len(result['content'])} characters")

        # Save the result to a file for inspection
        output_file = f"test_{result['chart_type']}.svg"
        with open(output_file, 'w') as f:
            f.write(result['content'])
        print(f"SVG chart saved to: {output_file}")

        # Check if it looks like valid SVG
        content = result['content'].strip()
        if content.startswith('<?xml') or content.startswith('<svg'):
            print("✅ Generated content appears to be valid SVG")
        else:
            print("⚠️  Generated content may not be valid SVG")
            print("First 200 characters:")
            print(content[:200])

        return True

    except Exception as e:
        print(f"❌ Error during chart generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Simple Chart SVG Generation with Matplotlib")
    print("=" * 50)

    # Test function structure first
    if not test_function_structure():
        print("❌ Function structure test failed, exiting")
        sys.exit(1)

    print("\n" + "=" * 50)

    # Test 1: Country economic data (bubble chart)
    country_data = """Country,GDP,Population,Region
United States,21.43,331,North America
China,14.34,1439,Asia
Germany,3.85,83,Europe
Japan,4.94,126,Asia
United Kingdom,2.83,67,Europe
France,2.72,68,Europe
India,2.87,1380,Asia
Brazil,1.61,215,South America
Canada,1.74,38,North America
Australia,1.39,26,Oceania"""

    print("Test 1: Country Economic Data (Bubble Chart)")
    success1 = test_svg_chart(country_data, "Create a bubble chart showing GDP vs Population by country with regional color coding")

    print("\n" + "=" * 50)

    # Test 2: Simple sales data (bar chart)
    sales_data = """Month,Sales
January,120000
February,135000
March,142000
April,128000
May,155000
June,168000"""

    print("Test 2: Sales Data (Bar Chart)")
    success2 = test_svg_chart(sales_data, "Create a bar chart showing monthly sales")

    print("\n" + "=" * 50)
    print("🏁 Test Summary")
    print(f"Test 1 (Bubble Chart): {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Test 2 (Bar Chart): {'✅ PASSED' if success2 else '❌ FAILED'}")

    if all([success1, success2]):
        print("\n🎉 SVG chart tests completed!")
        print("\n📊 Generated Files:")
        import glob
        svg_files = glob.glob("*.svg")
        for file in svg_files:
            size = os.path.getsize(file)
            print(f"  - {file} ({size:,} bytes)")
        print("\n💡 Open the SVG files in a browser or image viewer to see the charts!")
    else:
        print("\n⚠️  Some tests failed - check output above for details")
