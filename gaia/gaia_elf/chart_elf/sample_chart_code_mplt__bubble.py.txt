import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# ---- CHART SIZE SETUP ----
size = 'small'   # choose: 'small', 'med', 'large'

presets = {
    'small': {
        'figsize': (4, 3),
        'fontsize': 10,
        'title_fontsize': 12,
        'label_fontsize': 9,
        'legend_fontsize': 8,
        'bubble_mult': 5,
        'min_bubble': 15,
        'max_bubble': 120,
        'edge_width': 1,
        'annotate_quadrant': False,
        'show_source': False
    },
    'med': {
        'figsize': (8, 5),
        'fontsize': 13,
        'title_fontsize': 17,
        'label_fontsize': 12,
        'legend_fontsize': 11,
        'bubble_mult': 8,
        'min_bubble': 40,
        'max_bubble': 300,
        'edge_width': 2,
        'annotate_quadrant': True,
        'show_source': True
    },
    'large': {
        'figsize': (12, 8),
        'fontsize': 16,
        'title_fontsize': 22,
        'label_fontsize': 15,
        'legend_fontsize': 14,
        'bubble_mult': 12,
        'min_bubble': 100,
        'max_bubble': 700,
        'edge_width': 2.7,
        'annotate_quadrant': True,
        'show_source': True
    }
}
P = presets[size]

# ---- COLOR PALETTE ----
colors = {
    'Technology': '#212529',
    'Healthcare': '#7a8b74',
    'Finance': '#6b7a8b',
    'Energy': '#b89968',
    'Consumer': '#9b7d6b'
}
background = '#f4f5ef'
foreground = '#ffffff'
text_primary = '#212529'
text_secondary = '#6c757d'
grid_color = '#e9ecef'
border_color = '#ced4da'

# ---- DATA GENERATION ----
np.random.seed(42)
n_points = 25
categories = list(colors)
data = pd.DataFrame({
    'x_metric': np.random.normal(50, 20, n_points),
    'y_metric': np.random.normal(15, 8, n_points),
    'size_metric': np.random.exponential(30, n_points) + 10,
    'category': np.random.choice(categories, n_points),
    'company': [f'Company {i+1}' for i in range(n_points)]
})
data['x_metric'] = np.clip(data['x_metric'], -10, 100)
data['y_metric'] = np.clip(data['y_metric'], -5, 35)
data['size_metric'] = np.clip(data['size_metric'], 5, 150)

# ---- FIGURE SETUP ----
fig, ax = plt.subplots(figsize=P['figsize'])
fig.patch.set_facecolor(background)
ax.set_facecolor(foreground)

# ---- BUBBLE PLOTTING ----
for cat in categories:
    df = data[data['category'] == cat]
    sizes = np.clip(df['size_metric'] * P['bubble_mult'], P['min_bubble'], P['max_bubble'])
    ax.scatter(df['x_metric'], df['y_metric'], 
               s=sizes,
               c=colors[cat], label=cat,
               alpha=0.7,
               edgecolors=foreground,
               linewidth=P['edge_width'])

# ---- REFERENCE LINES ----
avg_x, avg_y = data['x_metric'].mean(), data['y_metric'].mean()
ax.axvline(x=avg_x, color='#6c757d', linestyle='--', alpha=0.5, linewidth=1, zorder=0)
ax.axhline(y=avg_y, color='#6c757d', linestyle='--', alpha=0.5, linewidth=1, zorder=0)

# ---- AXES AND TITLE ----
ax.set_title("Corporate Performance Matrix", fontsize=P['title_fontsize'], color=text_primary, fontweight='bold', loc='left', pad=8)
ax.set_xlabel('Revenue Growth (%)', fontsize=P['label_fontsize'], color=text_primary)
ax.set_ylabel('Profit Margin (%)', fontsize=P['label_fontsize'], color=text_primary)

for spine in ['top', 'right']:
    ax.spines[spine].set_visible(False)
ax.spines['bottom'].set_linewidth(1.7)
ax.spines['bottom'].set_color(text_primary)
ax.spines['left'].set_linewidth(1.7)
ax.spines['left'].set_color(text_primary)

ax.grid(True, alpha=0.23, color=grid_color, linewidth=1)
ax.set_axisbelow(True)

ax.tick_params(axis='both', labelsize=P['fontsize'], colors=text_secondary)

# ---- LEGEND ----
leg = ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=False,
                fontsize=P['legend_fontsize'])
leg.get_frame().set_facecolor('white')
leg.get_frame().set_edgecolor(border_color)
leg.get_frame().set_alpha(0.94)

# ---- QUADRANT ANNOTATION (optional) ----
if P['annotate_quadrant']:
    ax.text(data['x_metric'].max() * 0.88, data['y_metric'].max() * 0.87,
            'High Growth\nHigh Margin', fontsize=max(P['fontsize']-1, 9),
            color=text_secondary, ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.25", facecolor=foreground, edgecolor=grid_color, alpha=0.82))
    ax.text(data['x_metric'].min() * 0.5, data['y_metric'].max() * 0.87,
            'Low Growth\nHigh Margin', fontsize=max(P['fontsize']-1, 9),
            color=text_secondary, ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.25", facecolor=foreground, edgecolor=grid_color, alpha=0.82))

# ---- SOURCE ANNOTATION (optional) ----
if P['show_source']:
    fig.text(0.02, 0.02, "Source: Internal Analytics • Bubble size = market cap", 
             fontsize=max(P['fontsize']-2, 8), color=text_secondary, style='italic', ha='left')

plt.tight_layout()
plt.subplots_adjust(bottom=0.13, left=0.12, right=0.97, top=0.92)

plt.savefig(f"bubble_chart_{size}.png", dpi=200, bbox_inches='tight', facecolor=background)
plt.show()
