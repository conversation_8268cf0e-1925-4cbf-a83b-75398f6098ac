import logging
from typing import List, Dict, Any, <PERSON><PERSON>, <PERSON><PERSON>, Union
import json
import concurrent.futures
import multiprocessing
import time

from gaia.gaia_llm import gaia_llm

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

'''
Look at:

'''

class MultimodelJson:
    """
    A generic base class for querying multiple LLM models and aggregating JSON responses.
    Subclasses must implement _validate_result() to handle domain-specific data extraction.
    """

    def __init__(
        self,
        models: List[str],
        max_retries: int = 3,
        max_tokens: int = 4000,
    ):
        """
        Initializes the MultimodelJson instance.

        Args:
            models (List[str]): List of model names to query.
            max_retries (int): Maximum number of retries per model.
            max_tokens (int): Maximum number of tokens for LLM responses.
        """
        if not models:
            raise ValueError("The list of models must not be empty.")
        self.models = models
        self.max_retries = max_retries
        self.max_tokens = max_tokens
        self.llm_clients = self._initialize_llm_clients()

    def _initialize_llm_clients(self) -> Dict[str, gaia_llm.Json_LlmClient_Cached]:
        """
        Initializes LLM clients for each model.

        Returns:
            Dict[str, gaia_llm.Json_LlmClient_Cached]: A dictionary mapping model names to their LLM clients.
        """
        clients = {}
        for model in self.models:
            try:
                clients[model] = gaia_llm.Json_LlmClient_Cached(
                    model=model, max_tokens=self.max_tokens
                )
                logger.info(f"Initialized LLM client for model: {model}")
            except Exception as e:
                logger.error(f"Failed to initialize LLM client for model {model}: {e}")
                raise
        return clients

    def _generate_from_model(self, model: str, prompt: str, return_extended: bool = False) -> Optional[Any]:
        """
        Queries a single model with the given prompt.

        Args:
            model (str): The model name.
            prompt (str): The prompt to send to the model.
            return_extended (bool): Whether to return extended information including citations and thinking output.

        Returns:
            If return_extended is False:
                Optional[Any]: The validated JSON response from the model, or None if invalid.
            If return_extended is True:
                Tuple[Optional[Any], Dict[str, Any]]: A tuple containing:
                    - The validated JSON response (or None if invalid)
                    - A dictionary with extended information (citations, thinking, etc.)
        """
        # Enforce that the prompt mentions out_data
        prompt += "\n\nIMPORTANT: Please return valid JSON with a single top-level key 'out_data' only."

        client = self.llm_clients.get(model)
        if not client:
            logger.error(f"No LLM client found for model {model}.")
            return None if not return_extended else (None, {})

        for attempt in range(1, self.max_retries + 1):
            try:
                logger.debug(f"Model {model}: Attempt {attempt}")
                
                if return_extended:
                    # Use completion_json_extended to get more details
                    extended_result = client.completion_json_extended(
                        user_prompt=prompt,
                        log_context={"model": model, "attempt": attempt},
                    )
                    
                    # Extract content (the parsed JSON)
                    result = extended_result.get('content', {})
                    
                    # Create extended info dict
                    extended_info = {
                        'raw': extended_result.get('raw', {}),
                        'usage': extended_result.get('usage', {}),
                        'cost': extended_result.get('cost', {}),
                        'citations': extended_result.get('citations', []),
                        'thinking': extended_result.get('think')
                    }
                else:
                    # Use the original completion_json
                    result = client.completion_json(
                        user_prompt=prompt,
                        log_context={"model": model, "attempt": attempt},
                        override_cache=False,
                    )
                    extended_info = {}
                
                logger.debug(f"Model {model} response: {result}")

                items = self._validate_result(result, model)
                if items is not None:
                    logger.info(
                        f"Model {model} generated a valid result on attempt {attempt}."
                    )
                    return items if not return_extended else (items, extended_info)
                logger.warning(
                    f"Model {model} returned empty or invalid data on attempt {attempt}."
                )
            except Exception as e:
                logger.error(
                    f"Model {model} failed on attempt {attempt}: {e}", exc_info=True
                )

        logger.error(f"Model {model} failed after {self.max_retries} attempts.")
        return None if not return_extended else (None, {})

    def _validate_result(self, result: Any, model: str) -> Optional[Any]:
        """
        Must be implemented by subclasses.
        Validates the result returned by a model.

        Args:
            result (Any): The raw response from the model.
            model (str): The model name (for logging or custom logic).

        Returns:
            Optional[Any]: The validated and possibly transformed result, or None if invalid.
        """
        raise NotImplementedError(
            "Subclasses must implement _validate_result() to parse model results."
        )

    @staticmethod
    def _unwrap_out_data(data: dict, model: str, logger: logging.Logger) -> Optional[dict]:
        """
        Checks if 'out_data' is the top-level key or is wrapped in a single object key.
        If found, returns the *content inside* out_data (thereby removing the out_data layer).
        """
        if "out_data" in data:
            # Return just the inner portion of out_data
            return data["out_data"]

        # Possibly wrapped: e.g. {"data": {"out_data": {...}}}
        keys = list(data.keys())
        if len(keys) == 1:
            only_key = keys[0]
            sub_dict = data[only_key]
            if isinstance(sub_dict, dict) and "out_data" in sub_dict:
                logger.info(f"Unwrapping extra layer '{only_key}' for model {model}")
                # Return just the inner portion of out_data
                return sub_dict["out_data"]

        logger.error(
            f"Model {model} response does not have 'out_data' or single wrapper containing it. Keys: {list(data.keys())}"
        )
        return None


class PromptMultimodelJson(MultimodelJson):
    """
    A subclass of MultimodelJson that processes a single prompt and returns results
    along with operational metrics such as latency and error information.
    This class operates sequentially without parallelism.
    """

    def _validate_result(self, result: Any, model: str) -> Optional[Any]:
        """
        Validates that the result is a JSON object and checks for the 'out_data' key,
        finally returning only the inside of 'out_data'.
        """
        if not isinstance(result, dict):
            logger.error(f"Model {model} response is not a JSON object.")
            return None

        # Unwrap or verify that 'out_data' exists
        return self._unwrap_out_data(result, model, logger)

    def generate(
        self, prompt: str, return_extended: bool = False
    ) -> Union[
        Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]],
        Tuple[Dict[str, Any], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]
    ]:
        """
        Generates JSON responses from all models based on the provided prompt.

        Args:
            prompt (str): The prompt string to send to all models.
            return_extended (bool): Whether to return extended information including citations.

        Returns:
            If return_extended is False:
                Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]]:
                    - results: A dictionary mapping model names to their JSON responses (or None if failed).
                    - operational_results: A dictionary mapping model names to their operational metrics.
            
            If return_extended is True:
                Tuple[Dict[str, Any], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]:
                    - results: A dictionary mapping model names to their JSON responses (or None if failed).
                    - operational_results: A dictionary mapping model names to their operational metrics.
                    - extended_info: A dictionary mapping model names to extended information (citations, thinking, etc.)
        """
        prompt = prompt.strip()
        results: Dict[str, Any] = {}
        operational_results: Dict[str, Dict[str, Any]] = {}
        extended_info: Dict[str, Dict[str, Any]] = {} if return_extended else None

        logger.info("Generating JSON data sequentially.")

        for model in self.models:
            start_time = time.perf_counter()
            try:
                if return_extended:
                    result, model_extended_info = self._generate_from_model(model, prompt, return_extended=True)
                    if result is not None:
                        results[model] = result
                        extended_info[model] = model_extended_info
                        operational_results[model] = {
                            "latency": time.perf_counter() - start_time,
                            "error_code": 0,
                            "error_message": None,
                        }
                    else:
                        results[model] = None
                        extended_info[model] = model_extended_info
                        operational_results[model] = {
                            "latency": time.perf_counter() - start_time,
                            "error_code": 1,
                            "error_message": "Invalid or empty response.",
                        }
                else:
                    result = self._generate_from_model(model, prompt, return_extended=False)
                    latency = time.perf_counter() - start_time
                    if result is not None:
                        results[model] = result
                        operational_results[model] = {
                            "latency": latency,
                            "error_code": 0,
                            "error_message": None,
                        }
                    else:
                        results[model] = None
                        operational_results[model] = {
                            "latency": latency,
                            "error_code": 1,
                            "error_message": "Invalid or empty response.",
                        }
            except Exception as e:
                latency = time.perf_counter() - start_time
                results[model] = None
                operational_results[model] = {
                    "latency": latency,
                    "error_code": 2,
                    "error_message": str(e),
                }
                if return_extended:
                    extended_info[model] = {}

        if return_extended:
            return results, operational_results, extended_info
        else:
            return results, operational_results


class ParallelPromptMultimodelJson(MultimodelJson):
    """
    A subclass of MultimodelJson that processes a single prompt and returns results
    along with operational metrics such as latency and error information.
    This class operates in parallel using threads.
    """

    def _validate_result(self, result: Any, model: str) -> Optional[Any]:
        """
        Validates that the result is a JSON object and checks for 'out_data'.
        Returns only the content inside 'out_data'.
        """
        if not isinstance(result, dict):
            logger.error(f"Model {model} response is not a JSON object.")
            return None

        # Unwrap or verify that 'out_data' exists
        return self._unwrap_out_data(result, model, logger)

    def generate(
        self, prompt: str, return_extended: bool = False
    ) -> Union[
        Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]],
        Tuple[Dict[str, Any], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]
    ]:
        """
        Generates JSON responses from all models based on the provided prompt using parallel threads.

        Args:
            prompt (str): The prompt string to send to all models.
            return_extended (bool): Whether to return extended information including citations.

        Returns:
            If return_extended is False:
                Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]]:
                    - results: A dictionary mapping model names to their JSON responses (or None if failed).
                    - operational_results: A dictionary mapping model names to their operational metrics.
            
            If return_extended is True:
                Tuple[Dict[str, Any], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]:
                    - results: A dictionary mapping model names to their JSON responses (or None if failed).
                    - operational_results: A dictionary mapping model names to their operational metrics.
                    - extended_info: A dictionary mapping model names to extended information (citations, thinking, etc.)
        """
        prompt = prompt.strip()
        results: Dict[str, Any] = {}
        operational_results: Dict[str, Dict[str, Any]] = {}
        extended_info: Dict[str, Dict[str, Any]] = {} if return_extended else None

        def process_model(model: str):
            start_time = time.perf_counter()
            try:
                if return_extended:
                    result, model_extended_info = self._generate_from_model(model, prompt, return_extended=True)
                    latency = time.perf_counter() - start_time
                    if result is not None:
                        results[model] = result
                        extended_info[model] = model_extended_info
                        operational_results[model] = {
                            "latency": latency,
                            "error_code": 0,
                            "error_message": None,
                        }
                    else:
                        results[model] = None
                        extended_info[model] = model_extended_info
                        operational_results[model] = {
                            "latency": latency,
                            "error_code": 1,
                            "error_message": "Invalid or empty response.",
                        }
                else:
                    result = self._generate_from_model(model, prompt, return_extended=False)
                    latency = time.perf_counter() - start_time
                    if result is not None:
                        results[model] = result
                        operational_results[model] = {
                            "latency": latency,
                            "error_code": 0,
                            "error_message": None,
                        }
                    else:
                        results[model] = None
                        operational_results[model] = {
                            "latency": latency,
                            "error_code": 1,
                            "error_message": "Invalid or empty response.",
                        }
            except Exception as e:
                latency = time.perf_counter() - start_time
                results[model] = None
                operational_results[model] = {
                    "latency": latency,
                    "error_code": 2,
                    "error_message": str(e),
                }
                if return_extended:
                    extended_info[model] = {}

        logger.info("Generating JSON data in parallel using threads.")
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=min(32, len(self.models))
        ) as executor:
            executor.map(process_model, self.models)

        if return_extended:
            return results, operational_results, extended_info
        else:
            return results, operational_results


class MprocPromptMultimodelJson(MultimodelJson):
    """
    A subclass of MultimodelJson that processes a single prompt using multiprocessing.
    Each process re-initializes its own LLM client to avoid pickling or concurrency issues.
    """

    def _validate_result(self, result: Any, model: str) -> Optional[Any]:
        """
        Validates that the result is a JSON object and checks for 'out_data'.
        Returns only the content inside 'out_data'.
        """
        if not isinstance(result, dict):
            logger.error(f"[Process] Model {model} response is not a JSON object.")
            return None

        # Unwrap or verify that 'out_data' exists
        return self._unwrap_out_data(result, model, logger)

    def _process_model(
        self, model: str, prompt: str, return_extended: bool = False
    ) -> Union[
        Tuple[str, Optional[Any], Dict[str, Any]],
        Tuple[str, Optional[Any], Dict[str, Any], Dict[str, Any]]
    ]:
        """
        Wrapper function that each process calls to handle one model.

        Args:
            model (str): The model name.
            prompt (str): The prompt to send to the model.
            return_extended (bool): Whether to return extended information.

        Returns:
            If return_extended is False:
                A tuple: (model_name, validated_result_or_None, operational_metrics_dict)
            If return_extended is True:
                A tuple: (model_name, validated_result_or_None, operational_metrics_dict, extended_info_dict)
        """
        start_time = time.perf_counter()
        try:
            if return_extended:
                result, extended_info = self._generate_from_model(model, prompt, return_extended=True)
                latency = time.perf_counter() - start_time
                if result is not None:
                    return (
                        model,
                        result,
                        {
                            "latency": latency,
                            "error_code": 0,
                            "error_message": None,
                        },
                        extended_info
                    )
                else:
                    return (
                        model,
                        None,
                        {
                            "latency": latency,
                            "error_code": 1,
                            "error_message": "Invalid or empty response.",
                        },
                        extended_info
                    )
            else:
                result = self._generate_from_model(model, prompt, return_extended=False)
                latency = time.perf_counter() - start_time
                if result is not None:
                    return (
                        model,
                        result,
                        {
                            "latency": latency,
                            "error_code": 0,
                            "error_message": None,
                        },
                    )
                else:
                    return (
                        model,
                        None,
                        {
                            "latency": latency,
                            "error_code": 1,
                            "error_message": "Invalid or empty response.",
                        },
                    )
        except Exception as e:
            latency = time.perf_counter() - start_time
            if return_extended:
                return (
                    model,
                    None,
                    {
                        "latency": latency,
                        "error_code": 2,
                        "error_message": str(e),
                    },
                    {}
                )
            else:
                return (
                    model,
                    None,
                    {
                        "latency": latency,
                        "error_code": 2,
                        "error_message": str(e),
                    },
                )

    def generate(
        self, prompt: str, return_extended: bool = False
    ) -> Union[
        Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]],
        Tuple[Dict[str, Any], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]
    ]:
        """
        Generates JSON responses from all models in parallel using multiprocessing.
        Each process re-initializes its own client.

        Args:
            prompt (str): The prompt string to send to all models.
            return_extended (bool): Whether to return extended information including citations.

        Returns:
            If return_extended is False:
                Tuple[Dict[str, Any], Dict[str, Dict[str, Any]]]:
                    - results: A dict mapping model names -> JSON responses (or None if failed).
                    - operational_results: A dict mapping model names -> dict of metrics.
            
            If return_extended is True:
                Tuple[Dict[str, Any], Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]]]:
                    - results: A dict mapping model names -> JSON responses (or None if failed).
                    - operational_results: A dict mapping model names -> dict of metrics.
                    - extended_info: A dict mapping model names -> extended information (citations, thinking, etc.)
        """
        prompt = prompt.strip()
        logger.info(f"Generating JSON data in parallel using {min(len(self.models), multiprocessing.cpu_count())} processes.")

        # Prepare arguments as tuples for each model
        tasks = [(model, prompt, return_extended) for model in self.models]

        # results_list will hold a list of tuples - either 3 or 4 elements depending on return_extended
        results_list = []
        with multiprocessing.Pool(processes=min(len(self.models), multiprocessing.cpu_count())) as pool:
            results_list = pool.starmap(self._process_model, tasks)

        # Convert list of tuples to the desired dict form
        final_results: Dict[str, Any] = {}
        final_operational: Dict[str, Dict[str, Any]] = {}
        final_extended_info: Dict[str, Dict[str, Any]] = {} if return_extended else None

        if return_extended:
            for model, content, metrics, ext_info in results_list:
                final_results[model] = content
                final_operational[model] = metrics
                final_extended_info[model] = ext_info
            
            return final_results, final_operational, final_extended_info
        else:
            for model, content, metrics in results_list:
                final_results[model] = content
                final_operational[model] = metrics
            
            return final_results, final_operational


# Example usage of the classes
if __name__ == "__main__":
    # Define multiple models (ensure these names are correct and available in your environment)
    models = [
        "gemini/gemini-2.0-flash-001",
        "gemini/gemini-2.0-flash-thinking-exp",
        "o1-preview",
        "gpt-4o-mini",
        "gpt-4o",
        "gpt-4.1",
        "gpt-4.1-mini",
        "gpt-4.1-nano",
        "anthropic/claude-sonnet-4-20250514",
        "anthropic/claude-3-5-sonnet-20241022",
        "perplexity/sonar",
        #"gemini/gemini-2.0-flash-thinking-exp",
        #"o1-preview",
        #"gpt-4o-mini",
        #"gpt-4o",
        #"anthropic/claude-3-5-sonnet-20241022",
    ]

    prompt = """
List vendors and products for making and optimizing digital twins of factories and industrial
settings.  Write all textual fields in telegraphic style:

For each one, output a JSON dict with the following keys:
- vendor_name
- product_name
- pros : list of pros, each as telegraphic-style string
- cons : list of cons, each as telegraphic-style string
- setup: how does the initial setup work? 
- feat_raw_video: can it accept raw video of the factory floor?  Concisely mention how this works
- feat_optim_floor: can it suggest optimizating factory floor layout?  Concisely mention how this works

Return ONLY valid JSON, return just a JSON dict with a single json key named "out_data".
"""


    TEST_SEQ = True
    TEST_SEQ_EXTENDED = True  # Added to test the extended functionality
    TEST_PAR = False
    TEST_PAR_EXTENDED = False  # Added to test the extended functionality
    TEST_MPROC = False
    TEST_MPROC_EXTENDED = False  # Added to test the extended functionality

    if TEST_SEQ:
        try:
            prompt_generator = PromptMultimodelJson(models=models)
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            exit(1)

        try:
            results, operational = prompt_generator.generate(prompt)
            print("=== PromptMultimodelJson Results (Sequential) ===")
            for model, content in results.items():
                print(f"\nModel: {model}")
                print("Response:", json.dumps(content, indent=2))
            print("\nOperational Metrics:")
            for model, metrics in operational.items():
                print(f"\nModel: {model}")
                for key, value in metrics.items():
                    print(f"  {key}: {value}")
        except Exception as e:
            logger.error(f"An error occurred during generation: {e}", exc_info=True)
            exit(1)

    if TEST_SEQ_EXTENDED:
        try:
            prompt_generator = PromptMultimodelJson(models=models)
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            exit(1)
        
        try:
            # Call generate with return_extended=True to get extended information
            results, operational, extended_info = prompt_generator.generate(prompt, return_extended=True)
            print("\n=== PromptMultimodelJson Results with Extended Info (Sequential) ===")
            for model, content in results.items():
                print(f"\nModel: {model}")
                print("Response:", json.dumps(content, indent=2))
            
            print("\nExtended Information:")
            for model, info in extended_info.items():
                print(f"\nModel: {model}")
                if info.get('citations'):
                    print("\nCitations:")
                    for citation in info['citations']:
                        print(f"  {citation}")
                
                if info.get('thinking'):
                    print("\nThinking:")
                    print(f"  {info['thinking']}")
        except Exception as e:
            logger.error(f"An error occurred during extended generation: {e}", exc_info=True)
            exit(1)

    if TEST_PAR:
        try:
            parallel_prompt_generator = ParallelPromptMultimodelJson(models=models)
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            exit(1)

        try:
            parallel_results, parallel_operational = parallel_prompt_generator.generate(prompt)
            print("\n=== ParallelPromptMultimodelJson Results (Parallel) ===")
            for model, content in parallel_results.items():
                print(f"\nModel: {model}")
                print("Response:", json.dumps(content, indent=2))
            print("\nOperational Metrics:")
            for model, metrics in parallel_operational.items():
                print(f"\nModel: {model}")
                for key, value in metrics.items():
                    print(f"  {key}: {value}")
        except Exception as e:
            logger.error(f"An error occurred during parallel generation: {e}", exc_info=True)
            exit(1)

    if TEST_PAR_EXTENDED:
        try:
            parallel_prompt_generator = ParallelPromptMultimodelJson(models=models)
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            exit(1)

        try:
            # Call generate with return_extended=True to get extended information
            parallel_results, parallel_operational, parallel_extended = parallel_prompt_generator.generate(prompt, return_extended=True)
            print("\n=== ParallelPromptMultimodelJson Results with Extended Info (Parallel) ===")
            for model, content in parallel_results.items():
                print(f"\nModel: {model}")
                print("Response:", json.dumps(content, indent=2))
            
            print("\nExtended Information:")
            for model, info in parallel_extended.items():
                print(f"\nModel: {model}")
                if info.get('citations'):
                    print("\nCitations:")
                    for citation in info['citations']:
                        print(f"  {citation}")
                
                if info.get('thinking'):
                    print("\nThinking:")
                    print(f"  {info['thinking']}")
        except Exception as e:
            logger.error(f"An error occurred during parallel extended generation: {e}", exc_info=True)
            exit(1)

    if TEST_MPROC:
        try:
            mproc_prompt_generator = MprocPromptMultimodelJson(models=models)
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            exit(1)

        try:
            mproc_results, mproc_operational = mproc_prompt_generator.generate(prompt)
            print("\n=== MprocPromptMultimodelJson Results (Multiprocessing) ===")
            for model, content in mproc_results.items():
                print(f"\nModel: {model}")
                print("Response:", json.dumps(content, indent=2))
            print("\nOperational Metrics:")
            for model, metrics in mproc_operational.items():
                print(f"\nModel: {model}")
                for key, value in metrics.items():
                    print(f"  {key}: {value}")
        except Exception as e:
            logger.error(f"An error occurred during multiprocessing generation: {e}", exc_info=True)
            exit(1)
            
    if TEST_MPROC_EXTENDED:
        try:
            mproc_prompt_generator = MprocPromptMultimodelJson(models=models)
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
            exit(1)

        try:
            # Call generate with return_extended=True to get extended information
            mproc_results, mproc_operational, mproc_extended = mproc_prompt_generator.generate(prompt, return_extended=True)
            print("\n=== MprocPromptMultimodelJson Results with Extended Info (Multiprocessing) ===")
            for model, content in mproc_results.items():
                print(f"\nModel: {model}")
                print("Response:", json.dumps(content, indent=2))
            
            print("\nExtended Information:")
            for model, info in mproc_extended.items():
                print(f"\nModel: {model}")
                if info.get('citations'):
                    print("\nCitations:")
                    for citation in info['citations']:
                        print(f"  {citation}")
                
                if info.get('thinking'):
                    print("\nThinking:")
                    print(f"  {info['thinking']}")
        except Exception as e:
            logger.error(f"An error occurred during multiprocessing extended generation: {e}", exc_info=True)
            exit(1)
