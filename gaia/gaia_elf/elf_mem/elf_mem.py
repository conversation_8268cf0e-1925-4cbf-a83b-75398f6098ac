'''
# elf_mem
# ----
# store bank key value
# get bank key
# delete bank key
# list bank [key_prefix|None]

# memory banks are top level folders
# keys are folder/file within bank
# values are contents of {key}.json file in 


'''

import pandas as pd
import json
import os
import glob


bank_root="/var/lib/gaia/GAIA_FS/gaia_elf/elf_mem/banks/"

'''
# levels of memory:
    - external_cold : have memory bank name, but need to ask to even see key listings
    - symbolic_reference: keys in context, can request values
    - working_memory: keys + values in context


### Banks
- global/system/core
- global/system/tools

- market/{market_slug}
- org/{organization_id}

- project/{project_id}
- deal/{deal_id}

- user/{user_id}

- convo/{conversation_id}

'''

def _write_json(fn, data):
    with open(fn, "w") as f:
        f.write(json.dumps(data))

def _read_json(fn):
    with open(fn, "r") as f:
        data = json.loads(f.read())
        return data

def _get_key_path(bank, key):
    return bank_root + bank + "/" + key + ".json"    



def mem_store(bank, key, value):
    path = _get_key_path(bank, key)
    # Create directory with parents if it doesn't exist
    os.makedirs(os.path.dirname(path), exist_ok=True)
    _write_json(path, value)

def mem_get(bank, key):
    path = _get_key_path(bank, key)
    if not os.path.exists(path):
        return None
    return _read_json(path)

def mem_delete(bank, key):
    path = _get_key_path(bank, key)
    if os.path.exists(path):
        os.remove(path)
    # Return True if file was deleted, False if it didn't exist
    return not os.path.exists(path)

def mem_bank_list():
    # list all memory banks
    banks = []
    for root, dirs, files in os.walk(bank_root):
        for dir in dirs:
            banks.append(os.path.relpath(os.path.join(root, dir), bank_root))
    return banks

def mem_list(bank, key_prefix=None):
    path = bank_root + bank + "/"
    # Return empty list if bank directory doesn't exist
    if not os.path.exists(path):
        return []

    if key_prefix:
        path += key_prefix + "*"
    else:
        path += "*"
    files = glob.glob(path)
    keys = [os.path.basename(f).replace(".json", "") for f in files]
    return keys

def mem_bank_info(bank):
    """
    Get comprehensive status information about a memory bank.

    Args:
        bank (str): Name of the memory bank

    Returns:
        dict: Dictionary containing bank information
    """
    path = bank_root + bank + "/"

    # Initialize info dictionary
    info = {
        "bank_name": bank,
        "absolute_path": os.path.abspath(path),
        "exists": False,
        "number_of_keys": 0,
        "total_size_bytes": 0,
        "total_size_human": "0 B",
        "last_access_time": None,
        "last_modify_time": None,
        "creation_time": None,
        "error": None
    }

    try:
        # Check if bank directory exists
        if not os.path.exists(path):
            info["error"] = f"Bank directory does not exist: {path}"
            return info

        info["exists"] = True

        # Get directory stats
        dir_stat = os.stat(path)
        info["last_access_time"] = dir_stat.st_atime
        info["last_modify_time"] = dir_stat.st_mtime
        info["creation_time"] = dir_stat.st_ctime

        # Get all JSON files (keys) in the bank
        json_files = glob.glob(os.path.join(path, "*.json"))
        info["number_of_keys"] = len(json_files)

        # Calculate total size only
        total_size = 0

        for json_file in json_files:
            try:
                file_stat = os.stat(json_file)
                total_size += file_stat.st_size
            except OSError:
                # Skip files that can't be accessed
                continue

        info["total_size_bytes"] = total_size
        info["total_size_human"] = _format_bytes(total_size)

    except Exception as e:
        info["error"] = f"Error accessing bank: {str(e)}"

    return info

def _format_bytes(bytes_size):
    """Convert bytes to human readable format"""
    if bytes_size == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while bytes_size >= 1024 and i < len(size_names) - 1:
        bytes_size /= 1024.0
        i += 1

    return f"{bytes_size:.1f} {size_names[i]}"



def mem_test():
    # First, show all available banks
    print("=== Available Memory Banks ===")
    banks = mem_bank_list()
    for bank in banks:
        print(f"  - {bank}")
    print(f"Total banks: {len(banks)}\n")

    # Test with a specific bank
    bank = "global/system/core"
    key = "test_key"
    value = {"test": "value"}

    print(f"=== Testing Bank: {bank} ===")
    mem_store(bank, key, value)

    print("Keys in bank:", mem_list(bank))
    print("Retrieved value:", mem_get(bank, key))

    print("\nBank info:")
    print(pd.Series(mem_bank_info(bank)))

    print("\nKeys after test:", mem_list(bank))

    # Show updated bank list
    print(f"\n=== Updated Bank List ===")
    updated_banks = mem_bank_list()
    for bank in updated_banks:
        print(f"  - {bank}")
    print(f"Total banks: {len(updated_banks)}")


if __name__ == "__main__":
    mem_test()
