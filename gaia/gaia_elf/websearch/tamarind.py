"""
Tamarind - Market Research Data Collection and Analysis Tool

Overview:
This module collects, caches, and analyzes Total Addressable Market (TAM) and 
Compound Annual Growth Rate (CAGR) data for various market sectors through web search.

Key Components:
1. Data Collection: Uses web search and LLM-based extraction to gather market size estimates
2. Data Caching: Stores retrieved data in a persistent cache to improve performance
3. Data Analysis: Processes and summarizes market data with statistical measures
4. Visualization: Generates plots of market trends over time

Main Functions:
- tamarind_raw_data(): Processes sector data and returns analyzed results
- tamarind_generate__cached(): Retrieves data from cache or performs new web searches
- plot_tam_predict(): Visualizes market size predictions over time

Usage:
Call tamarind_raw_data() with a market sector name to get processed market data,
or use the visualization functions to generate plots of market trends.
"""

import gaia.gaia_elf.elf as elf
import slugify
from pprint import pprint as pp
from gaia.gaia_llm import gaia_llm
import pandas as pd
from gaia.core import gaia_goid
import json
from gaia.util.gen_util import filesystem
#from gaia.gaia_frames import gaia_frames
import os
from pdb import set_trace as st
from gaia.gaia_clerks import clerks
import pandas as pd
from gaia.multisearch_api import GaiaMultiSearch
from gaia.util.keyvalue import kvcommon, mproc_kv
from gaia.gaia_elf.websearch import websearch
import Levenshtein
import numpy as np
import datetime  # Import was missing but needed for the datetime functions
import multiprocessing  # Add this import for the new multiprocessing functionality
from functools import partial  # Add this for creating partial functions

EXCEPTION_BREAK=True

# Small decorator to trace function entry/exit
def trace_func(func):
    def wrapper(*args, **kwargs):
        print(f">>> ENTER: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            print(f"<<< EXIT: {func.__name__} -> {type(result)}")
            return result
        except Exception as e:
            print(f"<<< ERROR: {func.__name__} -> {e}")
            raise
    return wrapper

def std(x):
    return np.std(x)

tamarind_fullsearch_kv = mproc_kv.KeyValueStore_jsongz_sharded_nomanifest_hashed(folder_path='/var/lib/gaia/GAIA_FS/caches/tamarind/fullsearch/')
tamarind_page_kv = mproc_kv.KeyValueStore_jsongz_sharded_nomanifest_hashed(folder_path='/var/lib/gaia/GAIA_FS/caches/tamarind/page/')


DEFAULT_MODEL = 'anthropic/claude-sonnet-4-20250514'
#DEFAULT_MODEL = 'gpt-4o-mini'



llm = gaia_llm.Json_LlmClient_Cached(model=DEFAULT_MODEL,)
clerk = clerks.Clerk(client=llm)



@trace_func
def tamarid__generate__singlepage__low(url):
        
    if '.pdf' in url or '.PDF' in url:
        return None

    plaintext=''
    metadescr=''
    try:
        #breakpoint()
        record = babyfetch_cached.fetch_url_or_get_cached(url)
        html = record_to_html(record)
        plaintext = html_to_plain(html)
        metadescr = babyparse.html_to_meta_description(html)
        #breakpoint()

    except Exception as e:
        print(e)
        if EXCEPTION_BREAK: breakpoint()
        return None

    if metadescr is None:
        metadescr = ''
    if plaintext is None:
        plaintext = ''

    text = metadescr + '\n' + plaintext
    if len(text) > 10000:
        text = text[:10000]

    # now extract facts from this
    res = clerk.response_json_basic(transform='webelf_tam_singelarticle', input_text=text )
    print( "webelf_tam_singelarticle: result", type(res) )
    print("DEBUG: LLM RETURNED STRUCTURE:", json.dumps(res, indent=2, default=str))
    print(res)
    container = res 
    if 'content' in container:
        container = container['content']
    if 'json' in container:
        container = container['json']
    if 'additionalProperties' in container:
        container = container['additionalProperties']
    if type(container) == str:
        container = json.loads(container)
    if 'list' in container:
        container = container['list']

    print(f"DEBUG: About to create DataFrame from container: {container}")
    print(f"DEBUG: Container type: {type(container)}")

    # Handle different LLM output structures
    if type(container) == list:
        print(f"DEBUG: Container is a list with {len(container)} items")
        df = pd.DataFrame(container)
    elif type(container) == dict:
        # Check if it has the expected structure with estimates
        if 'estimates' in container:
            print("DEBUG: Container is dict with 'estimates' key, converting to DataFrame")
            df = pd.DataFrame([container])  # Wrap dict in list for DataFrame
        elif 'market_analysis' in container and 'list' in container['market_analysis']:
            print("DEBUG: Container has 'market_analysis.list' structure, extracting list")
            df = pd.DataFrame(container['market_analysis']['list'])
        else:
            print("DEBUG: Container is dict but no recognized structure, creating empty DataFrame")
            df = pd.DataFrame()
    else:
        print("DEBUG: Container is neither list nor dict, creating empty DataFrame")
        df = pd.DataFrame()

    print(f"DEBUG: Created DataFrame with shape {df.shape} and columns {list(df.columns)}")
    print(" -- ")
    print(" WEB ITEM ", url)
    print(" -- ")
    print(df)
    return df



@trace_func
def tamarid__generate__singlepage__cached(url):
    url_trunc= url[:128]
    if tamarind_page_kv.exists(url_trunc):
        res = tamarind_page_kv.get(url_trunc)
        if res is None:
            tamarind_page_kv.delete(url_trunc)
        else:
            return res

    res = tamarid__generate__singlepage__low(url)

    if res is not None:
        tamarind_page_kv.set(url_trunc, res)
    return res




def process_urls_for_market_data_sequential(filtered_results_web):
    """
    Original sequential processing of URLs for market data extraction.
    """
    df_set = []

    # Now loop over URLs from results
    for item in filtered_results_web:
        print(item)
        try:
            #breakpoint()
            df = tamarid__generate__singlepage__cached(item['link'])

            # force the article URL to match the one from the summary
            df['article_url'] = item['link']

            df_set.append(df)
        except Exception as e:
            print(e)
            # if EXCEPTION_BREAK: breakpoint()
            # interp: no items found so df is None
            continue

    return df_set


def process_urls_for_market_data_threadpool(filtered_results_web, max_workers=5):
    """
    Parallel processing of URLs for market data extraction using ThreadPoolExecutor.
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed

    def process_single_url(item):
        """Process a single URL and return the result"""
        try:
            print(item)
            df = tamarid__generate__singlepage__cached(item['link'])

            # force the article URL to match the one from the summary
            df['article_url'] = item['link']

            return df
        except Exception as e:
            print(f"Error processing {item['link']}: {e}")
            return None

    df_set = []

    # Process URLs in parallel (adjust max_workers based on your needs)
    max_workers = min(max_workers, len(filtered_results_web))  # Don't exceed number of URLs

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_item = {executor.submit(process_single_url, item): item for item in filtered_results_web}

        # Collect results as they complete
        for future in as_completed(future_to_item):
            item = future_to_item[future]
            try:
                df = future.result()
                if df is not None:
                    df_set.append(df)
            except Exception as e:
                print(f"Exception in future for {item['link']}: {e}")
                continue

    return df_set


def process_urls_for_market_data(filtered_results_web, method="threadpool", max_workers=5):
    """
    Process URLs for market data extraction with configurable parallelization.

    Args:
        filtered_results_web: List of URL items to process
        method: "sequential" or "threadpool"
        max_workers: Number of parallel workers (only used for threadpool)

    Returns:
        List of DataFrames with market data
    """
    if method == "sequential":
        return process_urls_for_market_data_sequential(filtered_results_web)
    elif method == "threadpool":
        return process_urls_for_market_data_threadpool(filtered_results_web, max_workers)
    else:
        raise ValueError(f"Unknown method: {method}. Use 'sequential' or 'threadpool'")


@trace_func
def tamarind_generate__fullsearch__low(search_phrase_sector, kind="market"):


    if kind == "market":
        search_phrase = search_phrase_sector + ' TAM CAGR'
    elif kind == "raw":
        search_phrase = search_phrase_sector
    else:
        raise ValueError(f"Unknown kind: {kind}")

    #search_types_selected = ['web', 'scholar', 'news', 'patents']

    # need to cache these too...
    results = websearch._get_search_results( search_phrase, num=10 )

    from gaia.gaia_elf.classify_elf import text_evaluator_cached_elf
    import logging

    logger = logging.getLogger(__name__)

    logger.info("🔍 MARKET RESEARCH: Starting search result relevance evaluation")
    logger.info(f"   Search phrase: {search_phrase}")
    logger.info(f"   Number of search results to evaluate: {len(results['web'])}")
    logger.info("   Purpose: Ranking search results by TAM/CAGR data relevance")

    classrate_categories = {
        'relevance': """For this item, based on title and snippet, how likely search item
looks like it contains Industry and/or Market CAGR and TAM data (NOT a stock or investment CAGR, and not just definitions or other content), probability 0.0 to 1.0
""",
    }
    evaluator = text_evaluator_cached_elf.TextEvaluatorElf( #_Mproc(
        model="anthropic/claude-sonnet-4-20250514",
        id_column="id",
        record_cache_folder=None,
        classrate_categories=classrate_categories,
        classes_to_explain=None,
        max_retries=1
    )
    def reduced_item(item):
        return {
            'title': item['title'],
            'snippet': item['snippet'],
        }

    results_web = results['web']
    reduced_results_dict = { str(idx) : reduced_item(item) for idx,item in enumerate(results_web)  }
    full_results_dict = { str(idx) : item for idx,item in enumerate(results_web)  }

    logger.info("🤖 MARKET RESEARCH: Starting LLM evaluation of search results...")
    df = evaluator.evaluate_texts( reduced_results_dict, override_record_cache=True, override_llm_cache=False,) # chunk_size = 50,)

    logger.info("✅ MARKET RESEARCH: Search result evaluation completed")
    logger.info(f"   Evaluated {len(df)} search results")
    if not df.empty and 'relevance' in df.columns:
        high_relevance = df[df['relevance'] > 0.7]
        logger.info(f"   High relevance results (>0.7): {len(high_relevance)}")
        logger.info(f"   Average relevance score: {df['relevance'].mean():.2f}")

    print(df)
    #breakpoint()

    # now look at ids in the df
    try:
        filtered_results_web = [ full_results_dict[id] for id in df['id'].tolist()  ]
    except Exception as e:
        print(e)
        ## if EXCEPTION_BREAK: breakpoint()
        # interpretation: there are NO relevant articles
        filtered_results_web = []


    print(filtered_results_web)

    #breakpoint()

    # a dataframe for each page
    df_set = []

    # Process URLs using configurable method
    df_results = process_urls_for_market_data(filtered_results_web, method="threadpool", max_workers=10) # sequential")
    df_set.extend(df_results)

    #breakpoint()
    # concat all results
    if len(df_set) > 0:
        df_all = pd.concat(df_set)
        print(df_all)
        # df_all.to_csv('/tmp/df_all.csv')
        df = df_all
        # make index sequential ints
        df.index = np.arange(len(df))

        #breakpoint()
        try:
            relat = relate.relationalize_ownedlist_col_dict( df, column_name='estimates',)
        except Exception as e:
            print(e)
            print("DEBUG: DataFrame processing failed, but we may have raw LLM data")
            # Instead of returning None, let's return whatever data we have
            # Check if df has any useful data we can return
            if not df.empty:
                print(f"DEBUG: Returning raw DataFrame with {len(df)} rows")
                return {'raw_data': df, 'processing_error': str(e)}
            else:
                print("DEBUG: No data to return")
                return None

        relat['parent'] = relat['parent'].set_index('parent_index')
        df_join = relat['child'].merge(
            relat['parent'], 
            left_on='parent_index', 
            right_index=True,  # Rename only columns from parent
        )
        df_join = df_join.drop(columns=['estimates'])

        relat['join'] = df_join

        if relat['join'].shape[0] > 0:
            df_set.append( relat['join'] )
            #st()

        print( relat['join'] )

        return relat

    return None










from gaia.babyspider.babyspider import BabyFetch
from gaia.babyspider.cached_babyfetch import CachedBabyFetch
from gaia.util.keyvalue.mproc_kv import KeyValueStore_jsongz_sharded, KeyValueStore_jsongz_sharded_nomanifest
from gaia.babyspider.babyparse import abs_to_relative_link, html_to_plain
from gaia.babyspider import babyparse
from gaia.util.relate import relate


def record_to_html(record):
    return BabyFetch().record_to_html(record)
def url_to_domain(url):
    return BabyFetch()._BabyFetch__url_to_domain(url)
def record_to_content(record):
    return BabyFetch().record_to_content(record)

gaia_base = '/var/lib/gaia/GAIA_FS'
BABYSPIDER_CACHE_PATH = './DATA/babyspider_main/'
kv = KeyValueStore_jsongz_sharded_nomanifest(folder_path=BABYSPIDER_CACHE_PATH)

babyfetch_cached = CachedBabyFetch(kv=kv)


@trace_func
def tamarind_generate__fullsearch__cached(search_phrase_sector, kind="market"):

    # exists in tamarind_fullsearch_kv cache?
    # if so, return that
    # if not, run search, cache it, and return it
    if tamarind_fullsearch_kv.exists(search_phrase_sector):
        raw_val = tamarind_fullsearch_kv.get(search_phrase_sector)
        if raw_val is None:
            tamarind_fullsearch_kv.delete(search_phrase_sector)
        else:
            raw_val_dict = json.loads(raw_val)
            # for each key in raw_val_dict, convert to pandas dataframe
            for key in raw_val_dict.keys():
                #breakpoint()
                raw_val_dict[key] = pd.DataFrame( json.loads(raw_val_dict[key]) )
            return raw_val_dict

    relat = tamarind_generate__fullsearch__low(search_phrase_sector, kind=kind)
    if relat == None:
        return None

    try:
        json_serializable_val = { key : relat[key].to_json() for key in relat.keys() }
        store_val =  json.dumps(json_serializable_val)
        tamarind_fullsearch_kv.set(search_phrase_sector, store_val)
        return relat
    except Exception as e:
        print(e)
        if EXCEPTION_BREAK: breakpoint()
        return None



def tamarind_raw__clean( results ):
    df = results['join']

    # now drop rows where market_name is significantly different to the search phrase
    # measure the difference using the Levenshtein distance
    # df['lev'] = df['market_name'].apply(lambda x: Levenshtein.distance(x, search_phrase_sector) )
    results['raw'] = df

    #df = df[ df['lev'] < 2 ]
    #df = df.drop(columns=['lev'])
    print(df)

    # include only rows where geo_scope is 'global'
    #breakpoint()
    df = df[ df['geo_scope']=='global' ]

    cur_year = datetime.datetime.now().year
    max_cur_year_lim = cur_year+1
    min_cur_year_lim = cur_year-3

    results['join'] = df


    df['start_year'] = df['start_year'].fillna(-1)
    df['start_year_cur_near'] = ( (df['start_year'] >= min_cur_year_lim) & (df['start_year'] <= max_cur_year_lim) ).astype(int)
    df['start_year_fut'] = ( (df['start_year'] > max_cur_year_lim) ).astype(int)

    df['end_year'] = df['end_year'].fillna(-1)
    df['end_year_cur_near'] = ( (df['end_year'] >= min_cur_year_lim) & (df['end_year'] <= max_cur_year_lim) ).astype(int)
    df['end_year_fut'] = ( (df['end_year'] > max_cur_year_lim) ).astype(int)


    df_near = df[ df['start_year_cur_near']==1 ]
    df_fut = df[ df['start_year_fut']==1 ]


    summary_near = df_near.groupby(['market_name','geo_scope']).agg({'tam_usdbb':['median',std],'cagr':'median','parent_index':'count','year':['min','max','mean','median',std]})
    #summary = summary.sort_values(by="median", ascending=False)
    print( summary_near )

    summary_fut = df_fut.groupby(['market_name','geo_scope']).agg({'tam_usdbb':['median',std],'cagr':'median','parent_index':'count','year':['min','max','mean','median',std]})
    #summary = summary.sort_values(by="median", ascending=False)
    print( summary_near )

    #st()

    results['summary_near'] = summary_near
    results['summary_fut'] = summary_near

    return results


'''
# estimate current TAM
import time, datetime

# xy plot: tam_usdbb vs year
import matplotlib.pyplot as plt
import seaborn as sns
sns.set_theme(style="whitegrid")



# draw a scatter plot of TAM vs year
# using plotly bubble chart
def tam_scatter( df_join ):
    import plotly
    import plotly.express as px

    fig = px.scatter(df_join, x="year", y="tam_usdbb",  color="geo_scope",
                    text="text_name", log_y=True, size_max=60, )

    # set label text size smaller
    fig.update_traces(textfont_size=10)

    # set image size to 1k x 1k
    fig.update_layout(width=1600, height=1200)
    return fig


def cagr_scatter( df_join ):
    import plotly
    import plotly.express as px

    fig = px.scatter(df_join, x="year", y="cagr",  color="geo_scope",
                    text="text_name", log_y=True, size_max=60, )
    fig.update_traces(textfont_size=10)

    # set image size to 1k x 1k
    fig.update_layout(width=1600, height=1200)

    #plt.show()
    return fig
    #fig.write_image("cagr_scatter_output.png")

'''



@trace_func
def do_tamarind( search_phrase_sector, kind="market" ):
    try:
        print(f"DEBUG: do_tamarind starting with sector: {search_phrase_sector}")
        search_phrase_slug = slugify.slugify(search_phrase_sector)
        folder = '/var/lib/gaia/GAIA_FS/tamarind/{}'.format(search_phrase_slug)
        filesystem.ensure_folder(folder)

        print("DEBUG: About to call tamarind_generate__fullsearch__cached")
        results = tamarind_generate__fullsearch__cached(search_phrase_sector, kind=kind)
        print(f"DEBUG: tamarind_generate__fullsearch__cached returned: {type(results)}")

        if results is None:
            print("DEBUG: results is None, returning None")
            return None

        print("DEBUG: About to print results")
        print(results)

        # Handle different result structures - sometimes 'join' key doesn't exist
        if 'join' in results and results['join'] is not None:
            df_join = results['join']
            df_join.to_csv(folder+"/tamarind_output_join.csv")
        else:
            print("DEBUG: No 'join' key in results, available keys:", list(results.keys()) if isinstance(results, dict) else type(results))
            # Try to save whatever data we have
            if isinstance(results, dict):
                import json
                with open(folder+"/tamarind_output_raw.json", "w") as f:
                    json.dump(results, f, indent=2, default=str)

        print(f"DEBUG: About to return results of type: {type(results)}")
        return results
    except Exception as e:
        print(f"DEBUG: Exception in do_tamarind: {e}")
        import traceback
        traceback.print_exc()
        return None


def do_tamarind_list( sector_list, ):
    results_dict = {}
    for sector in sector_list:
        results_dict[sector] = do_tamarind(sector)
    return results_dict


def do_tamarind_list_mproc(sector_list, k_procs=2):
    """Process multiple sectors in parallel using multiprocessing.
    
    Args:
        sector_list: List of sector names to process
        k_procs: Number of processes to use (default: 2)
    
    Returns:
        Dictionary mapping sector names to their processing results
    """
    # Strip and filter out empty sector names
    clean_sectors = [sector.strip() for sector in sector_list if sector.strip() and len(sector.strip()) > 1]
    
    # Create a pool with the specified number of processes
    with multiprocessing.Pool(processes=k_procs) as pool:
        # Process all sectors in parallel
        results = pool.map(do_tamarind, clean_sectors)
    
    # Combine results into a dictionary with sector names as keys
    return dict(zip(clean_sectors, results))


from gaia.gaia_frames import gaia_frames


def gather_to_frames():
    folder = '/var/lib/gaia/GAIA_FS/tamarind'
    dfs=[]
    rows=0
    # read all csv files in the sub folders beneath this folder
    for subfolder in os.listdir(folder):
        for file in os.listdir(os.path.join(folder, subfolder)):
            if file.endswith('.csv'):
                df = pd.read_csv(os.path.join(folder, subfolder, file))
                print(df.shape)

                drop_cols=['child_index','parent_index','Unnamed: 0']
                for col in drop_cols:
                    if col in df.columns:
                        df=df.drop(columns=[col])

                dfs.append(df)
                rows += df.shape[0]
                print(len(dfs), rows )
                #breakpoint()

    df_all = pd.concat(dfs)
    df_all = df_all.drop_duplicates()
    df_all = df_all.sort_values(by='cagr', ascending=False)

    gaia_frames.write_frame_pandas(pandas_df=df_all,
            section_slug='tamarind', 
            frame_name='tamarind__raw',  comment='Tamarind raw data')



import random

if __name__=="__main__":

    gather_to_frames()
    exit(44)
    if 0:
        sectors = """
    Electronics Assembly
    """
        if 0:
            #breakpoint()
            results = tamarind_generate__fullsearch__low(sectors.strip())
            print(results)

        results = tamarind_generate__fullsearch__cached(sectors.strip())
        print(results)

        do_tamarind( search_phrase_sector=sectors.strip(), )

        exit(44)

    if 0:
        for cagr in range(300,999,1):
            sector_string = f"Market CAGR {cagr}%"
            results_dict = do_tamarind(sector_string, kind="raw")
            print(results_dict)
        exit(44)

    if 1:
        sector_list = [ f"Market CAGR {cagr}%" for cagr in range(350,999,1)]
        random.shuffle( sector_list )
        #results_dict = do_tamarind_list_mproc(sector_list, k_procs=50)
        results_dict = do_tamarind_list(sector_list ) #, k_procs=50)
        exit()

    if 0:
        sector_string = "Market CAGR 47%"
        results_dict = do_tamarind(sector_string, kind="raw")
        print(results_dict)
        exit(44)

    if 0:
        fn="sectors.txt"
        print(fn)
        # read sectors.txt one sector per line
        sectors = open(fn).read()
        sector_list = [ll.strip() for ll in sectors.split('\n') if ll.strip() and len(ll.strip()) > 1]
        results_dict = do_tamarind_list(sector_list,)
        for sector, result in results_dict.items():
            print(f"\n=== Results for {sector} ===")
            pp(result)
        exit(44)


    if 0:
        fn="sectors.txt"
        print(fn)
        # read sectors.txt one sector per line
        sectors = open(fn).read()
        sector_list = [ll.strip() for ll in sectors.split('\n') if ll.strip() and len(ll.strip()) > 1]
        results_dict = do_tamarind_list_mproc(sector_list, k_procs=4)
        for sector, result in results_dict.items():
            print(f"\n=== Results for {sector} ===")
            pp(result)
        exit(44)
