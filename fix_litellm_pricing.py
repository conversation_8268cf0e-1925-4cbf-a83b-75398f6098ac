#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add missing Claude 4 models to LiteLLM pricing configuration
"""

import json
import os

# Path to the LiteLLM pricing file
PRICING_FILE = "/home/<USER>/.pyenv/versions/3.11.1/envs/agbase311/lib/python3.11/site-packages/litellm/model_prices_and_context_window.json"

# New Claude 4 models to add (based on gaia/gaia_llm/model_config.py)
NEW_MODELS = {
    "claude-opus-4-20250514": {
        "max_tokens": 4096,
        "max_input_tokens": 200000,
        "max_output_tokens": 4096,
        "input_cost_per_token": 0.000015,  # Estimated pricing for Opus 4
        "output_cost_per_token": 0.000075,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": True
    },
    "claude-sonnet-4-20250514": {
        "max_tokens": 4096,
        "max_input_tokens": 200000,
        "max_output_tokens": 4096,
        "input_cost_per_token": 0.000003,  # Similar to Claude 3 Sonnet
        "output_cost_per_token": 0.000015,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": True
    },
    "anthropic/claude-opus-4-20250514": {
        "max_tokens": 4096,
        "max_input_tokens": 200000,
        "max_output_tokens": 4096,
        "input_cost_per_token": 0.000015,
        "output_cost_per_token": 0.000075,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": True
    },
    "anthropic/claude-sonnet-4-20250514": {
        "max_tokens": 4096,
        "max_input_tokens": 200000,
        "max_output_tokens": 4096,
        "input_cost_per_token": 0.000003,
        "output_cost_per_token": 0.000015,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": True
    },
    "claude-3-7-sonnet-20250219": {
        "max_tokens": 4096,
        "max_input_tokens": 200000,
        "max_output_tokens": 4096,
        "input_cost_per_token": 0.000003,
        "output_cost_per_token": 0.000015,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": True
    },
    "anthropic/claude-3-7-sonnet-20250219": {
        "max_tokens": 4096,
        "max_input_tokens": 200000,
        "max_output_tokens": 4096,
        "input_cost_per_token": 0.000003,
        "output_cost_per_token": 0.000015,
        "litellm_provider": "anthropic",
        "mode": "chat",
        "supports_function_calling": True
    }
}

def main():
    # Check if file exists
    if not os.path.exists(PRICING_FILE):
        print(f"Error: Pricing file not found at {PRICING_FILE}")
        return False
    
    # Load existing pricing data
    try:
        with open(PRICING_FILE, 'r') as f:
            pricing_data = json.load(f)
        print(f"Loaded existing pricing data with {len(pricing_data)} models")
    except Exception as e:
        print(f"Error loading pricing file: {e}")
        return False
    
    # Add new models
    added_count = 0
    for model_name, model_config in NEW_MODELS.items():
        if model_name not in pricing_data:
            pricing_data[model_name] = model_config
            added_count += 1
            print(f"Added model: {model_name}")
        else:
            print(f"Model already exists: {model_name}")
    
    # Save updated pricing data
    try:
        with open(PRICING_FILE, 'w') as f:
            json.dump(pricing_data, f, indent=4)
        print(f"Successfully updated pricing file with {added_count} new models")
        print(f"Total models now: {len(pricing_data)}")
        return True
    except Exception as e:
        print(f"Error saving pricing file: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ LiteLLM pricing configuration updated successfully!")
    else:
        print("❌ Failed to update LiteLLM pricing configuration")
